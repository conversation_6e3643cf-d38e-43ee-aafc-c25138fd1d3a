[0.000000] (-) TimerEvent: {}
[0.000690] (loam_interface) JobQueued: {'identifier': 'loam_interface', 'dependencies': OrderedDict()}
[0.000740] (local_planner) JobQueued: {'identifier': 'local_planner', 'dependencies': OrderedDict()}
[0.000810] (sensor_scan_generation) JobQueued: {'identifier': 'sensor_scan_generation', 'dependencies': OrderedDict()}
[0.000889] (tare_planner) JobQueued: {'identifier': 'tare_planner', 'dependencies': OrderedDict()}
[0.000913] (terrain_analysis) JobQueued: {'identifier': 'terrain_analysis', 'dependencies': OrderedDict()}
[0.000937] (terrain_analysis_ext) JobQueued: {'identifier': 'terrain_analysis_ext', 'dependencies': OrderedDict()}
[0.000960] (vehicle_simulator) JobQueued: {'identifier': 'vehicle_simulator', 'dependencies': OrderedDict()}
[0.000989] (velodyne_description) JobQueued: {'identifier': 'velodyne_description', 'dependencies': OrderedDict()}
[0.001024] (velodyne_gazebo_plugins) JobQueued: {'identifier': 'velodyne_gazebo_plugins', 'dependencies': OrderedDict()}
[0.001054] (visualization_tools) JobQueued: {'identifier': 'visualization_tools', 'dependencies': OrderedDict()}
[0.001102] (waypoint_example) JobQueued: {'identifier': 'waypoint_example', 'dependencies': OrderedDict()}
[0.001126] (waypoint_rviz_plugin) JobQueued: {'identifier': 'waypoint_rviz_plugin', 'dependencies': OrderedDict()}
[0.001155] (velodyne_simulator) JobQueued: {'identifier': 'velodyne_simulator', 'dependencies': OrderedDict([('velodyne_description', '/home/<USER>/zhaoluye/install/velodyne_description'), ('velodyne_gazebo_plugins', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins')])}
[0.001207] (velodyne_description) JobStarted: {'identifier': 'velodyne_description'}
[0.004780] (velodyne_gazebo_plugins) JobStarted: {'identifier': 'velodyne_gazebo_plugins'}
[0.007437] (loam_interface) JobStarted: {'identifier': 'loam_interface'}
[0.010060] (local_planner) JobStarted: {'identifier': 'local_planner'}
[0.012758] (sensor_scan_generation) JobStarted: {'identifier': 'sensor_scan_generation'}
[0.015437] (tare_planner) JobStarted: {'identifier': 'tare_planner'}
[0.018118] (terrain_analysis) JobStarted: {'identifier': 'terrain_analysis'}
[0.020806] (terrain_analysis_ext) JobStarted: {'identifier': 'terrain_analysis_ext'}
[0.023388] (vehicle_simulator) JobStarted: {'identifier': 'vehicle_simulator'}
[0.026143] (visualization_tools) JobStarted: {'identifier': 'visualization_tools'}
[0.028922] (waypoint_example) JobStarted: {'identifier': 'waypoint_example'}
[0.031648] (waypoint_rviz_plugin) JobStarted: {'identifier': 'waypoint_rviz_plugin'}
[0.036812] (velodyne_description) JobProgress: {'identifier': 'velodyne_description', 'progress': 'cmake'}
[0.037119] (velodyne_description) JobProgress: {'identifier': 'velodyne_description', 'progress': 'build'}
[0.037627] (velodyne_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/velodyne_description', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/velodyne_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/velodyne_description'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.039267] (velodyne_gazebo_plugins) JobProgress: {'identifier': 'velodyne_gazebo_plugins', 'progress': 'cmake'}
[0.039775] (velodyne_gazebo_plugins) JobProgress: {'identifier': 'velodyne_gazebo_plugins', 'progress': 'build'}
[0.040066] (velodyne_gazebo_plugins) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.040951] (loam_interface) JobProgress: {'identifier': 'loam_interface', 'progress': 'cmake'}
[0.041487] (loam_interface) JobProgress: {'identifier': 'loam_interface', 'progress': 'build'}
[0.041776] (loam_interface) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/loam_interface', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/loam_interface', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/loam_interface'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.043249] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'cmake'}
[0.043433] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'build'}
[0.044041] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/local_planner', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/local_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.045294] (sensor_scan_generation) JobProgress: {'identifier': 'sensor_scan_generation', 'progress': 'cmake'}
[0.045504] (sensor_scan_generation) JobProgress: {'identifier': 'sensor_scan_generation', 'progress': 'build'}
[0.046191] (sensor_scan_generation) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/sensor_scan_generation', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/sensor_scan_generation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/sensor_scan_generation'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.048112] (tare_planner) JobProgress: {'identifier': 'tare_planner', 'progress': 'cmake'}
[0.048675] (tare_planner) JobProgress: {'identifier': 'tare_planner', 'progress': 'build'}
[0.049136] (tare_planner) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/tare_planner', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/tare_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/tare_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.051171] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'cmake'}
[0.051730] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'build'}
[0.052230] (terrain_analysis) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/terrain_analysis', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/terrain_analysis', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/terrain_analysis'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.054235] (terrain_analysis_ext) JobProgress: {'identifier': 'terrain_analysis_ext', 'progress': 'cmake'}
[0.054304] (terrain_analysis_ext) JobProgress: {'identifier': 'terrain_analysis_ext', 'progress': 'build'}
[0.054758] (terrain_analysis_ext) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/terrain_analysis_ext', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/terrain_analysis_ext', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/terrain_analysis_ext'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.056948] (vehicle_simulator) JobProgress: {'identifier': 'vehicle_simulator', 'progress': 'cmake'}
[0.058659] (vehicle_simulator) JobProgress: {'identifier': 'vehicle_simulator', 'progress': 'build'}
[0.059713] (vehicle_simulator) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/vehicle_simulator', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/vehicle_simulator', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/vehicle_simulator'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.062770] (visualization_tools) JobProgress: {'identifier': 'visualization_tools', 'progress': 'cmake'}
[0.063319] (visualization_tools) JobProgress: {'identifier': 'visualization_tools', 'progress': 'build'}
[0.064635] (visualization_tools) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/visualization_tools', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/visualization_tools', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/visualization_tools'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.066004] (waypoint_example) JobProgress: {'identifier': 'waypoint_example', 'progress': 'cmake'}
[0.066728] (waypoint_example) JobProgress: {'identifier': 'waypoint_example', 'progress': 'build'}
[0.067211] (waypoint_example) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/waypoint_example', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/waypoint_example', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/waypoint_example'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.074027] (waypoint_rviz_plugin) JobProgress: {'identifier': 'waypoint_rviz_plugin', 'progress': 'cmake'}
[0.074752] (waypoint_rviz_plugin) JobProgress: {'identifier': 'waypoint_rviz_plugin', 'progress': 'build'}
[0.075288] (waypoint_rviz_plugin) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.079039] (velodyne_description) CommandEnded: {'returncode': 0}
[0.079874] (velodyne_description) JobProgress: {'identifier': 'velodyne_description', 'progress': 'install'}
[0.089393] (velodyne_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/velodyne_description'], 'cwd': '/home/<USER>/zhaoluye/build/velodyne_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/velodyne_description'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.100154] (-) TimerEvent: {}
[0.108041] (sensor_scan_generation) StdoutLine: {'line': b'[100%] Built target sensorScanGeneration\n'}
[0.108877] (local_planner) StdoutLine: {'line': b'[ 50%] Built target localPlanner\n'}
[0.109488] (loam_interface) StdoutLine: {'line': b'[100%] Built target loamInterface\n'}
[0.115547] (local_planner) StdoutLine: {'line': b'[100%] Built target pathFollower\n'}
[0.115733] (tare_planner) StdoutLine: {'line': b'[  5%] Built target misc_utils\n'}
[0.116166] (velodyne_gazebo_plugins) StdoutLine: {'line': b'[100%] Built target gazebo_ros_velodyne_laser\n'}
[0.121881] (tare_planner) StdoutLine: {'line': b'[ 10%] Built target graph\n'}
[0.122057] (tare_planner) StdoutLine: {'line': b'[ 21%] Built target lidar_model\n'}
[0.122139] (tare_planner) StdoutLine: {'line': b'[ 21%] Built target pointcloud_utils\n'}
[0.122198] (sensor_scan_generation) CommandEnded: {'returncode': 0}
[0.122569] (tare_planner) StdoutLine: {'line': b'[ 26%] Built target tsp_solver\n'}
[0.122641] (velodyne_description) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.122748] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/launch\n'}
[0.125138] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/launch/example.launch.py\n'}
[0.125256] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes\n'}
[0.125317] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/HDL32E_scan.stl\n'}
[0.125366] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_scan.stl\n'}
[0.125412] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_scan.dae\n'}
[0.125458] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/HDL32E_scan.dae\n'}
[0.125503] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_base_2.stl\n'}
[0.125550] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/HDL32E_base.dae\n'}
[0.125594] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/HDL32E_base.stl\n'}
[0.125638] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_base_1.dae\n'}
[0.125682] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_base_1.stl\n'}
[0.125726] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_base_2.dae\n'}
[0.125781] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/rviz\n'}
[0.125830] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/rviz/example.rviz\n'}
[0.127417] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/urdf\n'}
[0.127955] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/urdf/example.urdf.xacro\n'}
[0.128076] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/urdf/VLP-16.urdf.xacro\n'}
[0.128235] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/urdf/HDL-32E.urdf.xacro\n'}
[0.128287] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/world\n'}
[0.128325] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/world/example.world\n'}
[0.128358] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/velodyne_description.dsv\n'}
[0.128392] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/ament_index/resource_index/package_run_dependencies/velodyne_description\n'}
[0.128424] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/ament_index/resource_index/parent_prefix_path/velodyne_description\n'}
[0.128455] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/ament_prefix_path.sh\n'}
[0.130220] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/ament_prefix_path.dsv\n'}
[0.130331] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/path.sh\n'}
[0.130389] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/path.dsv\n'}
[0.130440] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/local_setup.bash\n'}
[0.130496] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/local_setup.sh\n'}
[0.130540] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/local_setup.zsh\n'}
[0.130584] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/local_setup.dsv\n'}
[0.130627] (velodyne_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.dsv\n'}
[0.130669] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/ament_index/resource_index/packages/velodyne_description\n'}
[0.130712] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/cmake/velodyne_descriptionConfig.cmake\n'}
[0.130755] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/cmake/velodyne_descriptionConfig-version.cmake\n'}
[0.130798] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.xml\n'}
[0.130850] (tare_planner) StdoutLine: {'line': b'[ 31%] Built target exploration_path\n'}
[0.130912] (tare_planner) StdoutLine: {'line': b'[ 36%] Built target pointcloud_manager\n'}
[0.130963] (tare_planner) StdoutLine: {'line': b'[ 42%] Built target rolling_occupancy_grid\n'}
[0.131017] (terrain_analysis_ext) StdoutLine: {'line': b'[100%] Built target terrainAnalysisExt\n'}
[0.131085] (tare_planner) StdoutLine: {'line': b'[ 47%] Built target tare_visualizer\n'}
[0.131136] (waypoint_rviz_plugin) StdoutLine: {'line': b'[ 16%] \x1b[34m\x1b[1mAutomatic MOC for target waypoint_rviz_plugin\x1b[0m\n'}
[0.131200] (sensor_scan_generation) JobProgress: {'identifier': 'sensor_scan_generation', 'progress': 'install'}
[0.131220] (sensor_scan_generation) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/sensor_scan_generation'], 'cwd': '/home/<USER>/zhaoluye/build/sensor_scan_generation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/sensor_scan_generation'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.131613] (tare_planner) StdoutLine: {'line': b'[ 52%] Built target navigationBoundary\n'}
[0.132351] (loam_interface) CommandEnded: {'returncode': 0}
[0.132771] (visualization_tools) StdoutLine: {'line': b'[  0%] Built target ament_cmake_python_copy_visualization_tools\n'}
[0.133064] (loam_interface) JobProgress: {'identifier': 'loam_interface', 'progress': 'install'}
[0.133624] (loam_interface) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/loam_interface'], 'cwd': '/home/<USER>/zhaoluye/build/loam_interface', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/loam_interface'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.134648] (velodyne_description) CommandEnded: {'returncode': 0}
[0.135501] (sensor_scan_generation) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.135934] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/lib/sensor_scan_generation/sensorScanGeneration\n'}
[0.135996] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/launch\n'}
[0.136049] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/launch/sensor_scan_generation.launch\n'}
[0.136088] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/ament_index/resource_index/package_run_dependencies/sensor_scan_generation\n'}
[0.136126] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/ament_index/resource_index/parent_prefix_path/sensor_scan_generation\n'}
[0.136282] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/environment/ament_prefix_path.sh\n'}
[0.136325] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/environment/ament_prefix_path.dsv\n'}
[0.136362] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/environment/path.sh\n'}
[0.136399] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/environment/path.dsv\n'}
[0.136436] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.bash\n'}
[0.136472] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.sh\n'}
[0.136508] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.zsh\n'}
[0.136544] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.dsv\n'}
[0.136582] (sensor_scan_generation) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv\n'}
[0.136618] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/ament_index/resource_index/packages/sensor_scan_generation\n'}
[0.136655] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/cmake/sensor_scan_generationConfig.cmake\n'}
[0.136694] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/cmake/sensor_scan_generationConfig-version.cmake\n'}
[0.136731] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.xml\n'}
[0.137215] (terrain_analysis) StdoutLine: {'line': b'[100%] Built target terrainAnalysis\n'}
[0.138604] (vehicle_simulator) StdoutLine: {'line': b'[100%] Built target vehicleSimulator\n'}
[0.139105] (waypoint_rviz_plugin) StdoutLine: {'line': b'[ 16%] Built target waypoint_rviz_plugin_autogen\n'}
[0.139585] (visualization_tools) StdoutLine: {'line': b'[100%] Built target visualizationTools\n'}
[0.141868] (waypoint_example) StdoutLine: {'line': b'[100%] Built target waypointExample\n'}
[0.147598] (velodyne_description) JobEnded: {'identifier': 'velodyne_description', 'rc': 0}
[0.149097] (loam_interface) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.149509] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/lib/loam_interface/loamInterface\n'}
[0.149625] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/launch\n'}
[0.149722] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/launch/loam_interface.launch\n'}
[0.149815] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/ament_index/resource_index/package_run_dependencies/loam_interface\n'}
[0.149906] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/ament_index/resource_index/parent_prefix_path/loam_interface\n'}
[0.150001] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/environment/ament_prefix_path.sh\n'}
[0.150647] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/environment/ament_prefix_path.dsv\n'}
[0.150717] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/environment/path.sh\n'}
[0.150754] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/environment/path.dsv\n'}
[0.150785] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/local_setup.bash\n'}
[0.150816] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/local_setup.sh\n'}
[0.150852] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/local_setup.zsh\n'}
[0.150887] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/local_setup.dsv\n'}
[0.150917] (loam_interface) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.dsv\n'}
[0.150946] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/ament_index/resource_index/packages/loam_interface\n'}
[0.150975] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/cmake/loam_interfaceConfig.cmake\n'}
[0.151014] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/cmake/loam_interfaceConfig-version.cmake\n'}
[0.151044] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.xml\n'}
[0.151073] (tare_planner) StdoutLine: {'line': b'[ 57%] Built target viewpoint\n'}
[0.151117] (tare_planner) StdoutLine: {'line': b'[ 63%] Built target rolling_grid\n'}
[0.151153] (velodyne_gazebo_plugins) CommandEnded: {'returncode': 0}
[0.151656] (velodyne_gazebo_plugins) JobProgress: {'identifier': 'velodyne_gazebo_plugins', 'progress': 'install'}
[0.152064] (velodyne_gazebo_plugins) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins'], 'cwd': '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.152893] (tare_planner) StdoutLine: {'line': b'[ 68%] Built target planning_env\n'}
[0.153313] (local_planner) CommandEnded: {'returncode': 0}
[0.153910] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'install'}
[0.154777] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/local_planner'], 'cwd': '/home/<USER>/zhaoluye/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/local_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.156243] (terrain_analysis_ext) CommandEnded: {'returncode': 0}
[0.156799] (terrain_analysis_ext) JobProgress: {'identifier': 'terrain_analysis_ext', 'progress': 'install'}
[0.157340] (terrain_analysis_ext) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/terrain_analysis_ext'], 'cwd': '/home/<USER>/zhaoluye/build/terrain_analysis_ext', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/terrain_analysis_ext'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.158325] (sensor_scan_generation) CommandEnded: {'returncode': 0}
[0.165668] (waypoint_rviz_plugin) StdoutLine: {'line': b'[100%] Built target waypoint_rviz_plugin\n'}
[0.169145] (sensor_scan_generation) JobEnded: {'identifier': 'sensor_scan_generation', 'rc': 0}
[0.169704] (loam_interface) CommandEnded: {'returncode': 0}
[0.175668] (tare_planner) StdoutLine: {'line': b'[ 73%] Built target keypose_graph\n'}
[0.178679] (loam_interface) JobEnded: {'identifier': 'loam_interface', 'rc': 0}
[0.179024] (vehicle_simulator) CommandEnded: {'returncode': 0}
[0.180145] (vehicle_simulator) JobProgress: {'identifier': 'vehicle_simulator', 'progress': 'install'}
[0.180163] (vehicle_simulator) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/vehicle_simulator'], 'cwd': '/home/<USER>/zhaoluye/build/vehicle_simulator', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/vehicle_simulator'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.180809] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.181063] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib/libgazebo_ros_velodyne_laser.so\n'}
[0.181103] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/include/velodyne_gazebo_plugins\n'}
[0.181136] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/include/velodyne_gazebo_plugins/GazeboRosVelodyneLaser.hpp\n'}
[0.181169] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/environment/library_path.sh\n'}
[0.181200] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/environment/library_path.dsv\n'}
[0.181258] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/ament_index/resource_index/package_run_dependencies/velodyne_gazebo_plugins\n'}
[0.181289] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/ament_index/resource_index/parent_prefix_path/velodyne_gazebo_plugins\n'}
[0.181322] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/environment/ament_prefix_path.sh\n'}
[0.181352] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/environment/ament_prefix_path.dsv\n'}
[0.181381] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/environment/path.sh\n'}
[0.181411] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/environment/path.dsv\n'}
[0.181440] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/local_setup.bash\n'}
[0.181470] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/local_setup.sh\n'}
[0.181500] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/local_setup.zsh\n'}
[0.181529] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/local_setup.dsv\n'}
[0.181559] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.dsv\n'}
[0.181588] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/ament_index/resource_index/packages/velodyne_gazebo_plugins\n'}
[0.181617] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[0.181647] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[0.181713] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.181748] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/cmake/velodyne_gazebo_pluginsConfig.cmake\n'}
[0.181780] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/cmake/velodyne_gazebo_pluginsConfig-version.cmake\n'}
[0.181810] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.xml\n'}
[0.181842] (terrain_analysis) CommandEnded: {'returncode': 0}
[0.182035] (local_planner) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.182108] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/lib/local_planner/localPlanner\n'}
[0.182145] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/lib/local_planner/pathFollower\n'}
[0.182177] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/launch\n'}
[0.182209] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/launch/local_planner.launch.py\n'}
[0.182261] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/launch/local_planner.launch\n'}
[0.182296] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths\n'}
[0.182328] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/startPaths.ply\n'}
[0.182359] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/correspondences.txt\n'}
[0.182390] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/pathList.ply\n'}
[0.182421] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/paths.ply\n'}
[0.182451] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/path_generator.m\n'}
[0.182482] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/config\n'}
[0.182512] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/ament_index/resource_index/package_run_dependencies/local_planner\n'}
[0.182542] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/ament_index/resource_index/parent_prefix_path/local_planner\n'}
[0.182571] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/environment/ament_prefix_path.sh\n'}
[0.182602] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/environment/ament_prefix_path.dsv\n'}
[0.182631] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/environment/path.sh\n'}
[0.182661] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/environment/path.dsv\n'}
[0.182690] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/local_setup.bash\n'}
[0.182719] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/local_setup.sh\n'}
[0.182748] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/local_setup.zsh\n'}
[0.182777] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/local_setup.dsv\n'}
[0.182806] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.dsv\n'}
[0.182835] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/ament_index/resource_index/packages/local_planner\n'}
[0.182865] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/cmake/local_plannerConfig.cmake\n'}
[0.182894] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/cmake/local_plannerConfig-version.cmake\n'}
[0.182923] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.xml\n'}
[0.183076] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'install'}
[0.183709] (terrain_analysis) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/terrain_analysis'], 'cwd': '/home/<USER>/zhaoluye/build/terrain_analysis', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/terrain_analysis'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.184993] (terrain_analysis_ext) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.185687] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/lib/terrain_analysis_ext/terrainAnalysisExt\n'}
[0.186222] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/launch\n'}
[0.188821] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/launch/terrain_analysis_ext.launch\n'}
[0.190246] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/ament_index/resource_index/package_run_dependencies/terrain_analysis_ext\n'}
[0.190400] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/ament_index/resource_index/parent_prefix_path/terrain_analysis_ext\n'}
[0.190596] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/ament_prefix_path.sh\n'}
[0.190722] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/ament_prefix_path.dsv\n'}
[0.190934] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/path.sh\n'}
[0.191049] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/path.dsv\n'}
[0.191158] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.bash\n'}
[0.191275] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.sh\n'}
[0.191387] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.zsh\n'}
[0.191499] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.dsv\n'}
[0.191599] (terrain_analysis_ext) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv\n'}
[0.191724] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/ament_index/resource_index/packages/terrain_analysis_ext\n'}
[0.192085] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/cmake/terrain_analysis_extConfig.cmake\n'}
[0.192377] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/cmake/terrain_analysis_extConfig-version.cmake\n'}
[0.192678] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.xml\n'}
[0.192881] (waypoint_example) CommandEnded: {'returncode': 0}
[0.193712] (vehicle_simulator) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.193933] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/lib/vehicle_simulator/vehicleSimulator\n'}
[0.194469] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch\n'}
[0.194763] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_indoor.launch\n'}
[0.194956] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_real_robot_map.launch\n'}
[0.195241] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_campus.launch\n'}
[0.195291] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_real_robot.launch\n'}
[0.195326] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_forest.launch\n'}
[0.195370] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/vehicle_simulator.launch\n'}
[0.195413] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_garage.launch\n'}
[0.195444] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_tunnel.launch\n'}
[0.195475] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/log\n'}
[0.195506] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/log/metrics_2025-8-18-15-59-20.txt\n'}
[0.195536] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/log/metrics_2025-8-14-15-41-31.txt\n'}
[0.195570] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/log/trajectory_2025-8-18-15-59-20.txt\n'}
[0.195600] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/log/trajectory_2025-8-14-15-41-31.txt\n'}
[0.195629] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh\n'}
[0.195658] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus\n'}
[0.195687] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/model.sdf\n'}
[0.195717] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/preview\n'}
[0.195766] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/preview/pointcloud.ply\n'}
[0.195797] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/preview/overview.png\n'}
[0.195827] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/model.config\n'}
[0.195856] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes\n'}
[0.195885] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/texture_1001.png.001.png\n'}
[0.195913] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/photo_2020-11-01_11-54-02.jpg\n'}
[0.195943] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/leaf3.png.002.png\n'}
[0.195972] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/texture_1004.png.001.png\n'}
[0.196014] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/bark1.jpg\n'}
[0.196044] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/photo_2020-11-01_11-56-17.jpg\n'}
[0.196122] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/texture_1005.png.001.png\n'}
[0.196157] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/texture_1002.png.001.png\n'}
[0.196187] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/campus.dae\n'}
[0.196216] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/texture_1003.png.001.png\n'}
[0.196245] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest\n'}
[0.196273] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1\n'}
[0.196302] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/model.sdf\n'}
[0.196385] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials\n'}
[0.196415] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials/scripts\n'}
[0.196445] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials/scripts/house_1.material\n'}
[0.196474] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials/textures\n'}
[0.196503] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials/textures/House_1_Spec.png\n'}
[0.196532] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials/textures/House_1_Normal.png\n'}
[0.197071] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials/textures/House_1_Diffuse.png\n'}
[0.197104] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/model-1_4.sdf\n'}
[0.197135] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/model.config\n'}
[0.197166] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/model-1_3.sdf\n'}
[0.197196] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/meshes\n'}
[0.197226] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/meshes/house_1.dae\n'}
[0.197256] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall\n'}
[0.197284] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/model.sdf\n'}
[0.197313] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/materials\n'}
[0.197342] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/materials/scripts\n'}
[0.197371] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/materials/scripts/grey_wall.material\n'}
[0.197399] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/materials/textures\n'}
[0.197427] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/materials/textures/grey_wall.png\n'}
[0.197455] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/model-1_4.sdf\n'}
[0.197484] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/model.config\n'}
[0.197511] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/model-1_3.sdf\n'}
[0.197541] (waypoint_example) JobProgress: {'identifier': 'waypoint_example', 'progress': 'install'}
[0.197553] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain\n'}
[0.197587] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/model.sdf\n'}
[0.197664] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/materials\n'}
[0.197697] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/materials/scripts\n'}
[0.197727] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/materials/scripts/grass.material\n'}
[0.197758] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/materials/textures\n'}
[0.197794] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/materials/textures/grass_dry.png\n'}
[0.197821] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/materials/textures/heightmap.png\n'}
[0.197849] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/model.config\n'}
[0.197877] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane\n'}
[0.197906] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/model.sdf\n'}
[0.198131] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials\n'}
[0.198163] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials/scripts\n'}
[0.198191] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials/scripts/grass.material\n'}
[0.198220] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials/textures\n'}
[0.198249] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials/textures/grass_dry.png\n'}
[0.198278] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials/textures/flat_normal.png\n'}
[0.198306] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials/textures/Grass_Albedo.jpg\n'}
[0.198334] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/model.config\n'}
[0.198363] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/preview\n'}
[0.198392] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/preview/pointcloud.ply\n'}
[0.198421] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/preview/overview.png\n'}
[0.198449] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2\n'}
[0.198478] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/model.sdf\n'}
[0.198507] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/materials\n'}
[0.198536] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/materials/scripts\n'}
[0.198563] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/materials/scripts/house_2.material\n'}
[0.198590] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/materials/textures\n'}
[0.198617] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/materials/textures/House_2_Diffuse.png\n'}
[0.198664] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/model-1_4.sdf\n'}
[0.198694] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/model.config\n'}
[0.198723] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/model-1_3.sdf\n'}
[0.198757] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/meshes\n'}
[0.198785] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/meshes/house_2.dae\n'}
[0.198814] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree\n'}
[0.198842] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/model.sdf\n'}
[0.198870] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials\n'}
[0.198901] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/scripts\n'}
[0.198929] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/scripts/oak_tree.material\n'}
[0.198957] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/scripts/caster_vp.glsl\n'}
[0.198988] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/scripts/caster_fp.glsl\n'}
[0.199018] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/textures\n'}
[0.199045] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/textures/branch_diffuse.png\n'}
[0.199074] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/textures/bark_diffuse.png\n'}
[0.199193] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/model.config\n'}
[0.199224] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/meshes\n'}
[0.199255] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/meshes/oak_tree.dae\n'}
[0.199285] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree\n'}
[0.199313] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/model.sdf\n'}
[0.199342] (waypoint_example) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/waypoint_example'], 'cwd': '/home/<USER>/zhaoluye/build/waypoint_example', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/waypoint_example'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.199703] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/materials\n'}
[0.199745] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/materials/scripts\n'}
[0.199779] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/materials/scripts/pine_tree.material\n'}
[0.199811] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/materials/textures\n'}
[0.199841] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/materials/textures/branch_2_diffuse.png\n'}
[0.199871] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/materials/textures/bark_diffuse.png\n'}
[0.199900] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/model.config\n'}
[0.199930] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/meshes\n'}
[0.199961] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/meshes/pine_tree.dae\n'}
[0.199995] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor\n'}
[0.200026] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/model.sdf\n'}
[0.200056] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/preview\n'}
[0.200084] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/preview/pointcloud.ply\n'}
[0.200113] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/preview/overview.png\n'}
[0.200144] (-) TimerEvent: {}
[0.200235] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/model.config\n'}
[0.200279] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/meshes\n'}
[0.200311] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/meshes/indoor.dae\n'}
[0.200340] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/meshes/imgonline-com-ua-tile-PQCma89EHs3BX1.jpg\n'}
[0.200371] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/meshes/imgonline-com-ua-tile-K49qRoCnvbl.jpg\n'}
[0.200402] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/meshes/imgonline-com-ua-tile-RySXiqZBriXUB4J.jpg\n'}
[0.200433] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel\n'}
[0.200464] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/model.sdf\n'}
[0.200494] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/preview\n'}
[0.200523] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/preview/pointcloud.ply\n'}
[0.200552] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/preview/overview.png\n'}
[0.200581] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/model.config\n'}
[0.200609] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/meshes\n'}
[0.200638] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/meshes/tunnel.dae\n'}
[0.200666] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/meshes/wall_texture.jpg\n'}
[0.200694] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage\n'}
[0.200813] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/model.sdf\n'}
[0.200845] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/preview\n'}
[0.200874] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/preview/pointcloud.ply\n'}
[0.200903] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/preview/overview.png\n'}
[0.200932] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/model.config\n'}
[0.200963] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/meshes\n'}
[0.200998] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/meshes/imgonline-com-ua-tile-pLFAF53NCY1Spr.jpg\n'}
[0.201034] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/meshes/imgonline-com-ua-tile-turvdQ7ZY0r.jpg\n'}
[0.201076] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/meshes/blue-concrete-texture-3.jpg\n'}
[0.201105] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/meshes/imgonline-com-ua-tile-RySXiqZBriXUB4J.jpg\n'}
[0.201135] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/meshes/garage.dae\n'}
[0.201164] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/rviz\n'}
[0.201193] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/rviz/vehicle_simulator.rviz\n'}
[0.201220] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/urdf\n'}
[0.201248] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/urdf/robot.urdf.xacro\n'}
[0.201276] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/urdf/camera.urdf.xacro\n'}
[0.201303] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/urdf/lidar.urdf.xacro\n'}
[0.201331] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/urdf/robot.sdf\n'}
[0.201500] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world\n'}
[0.201531] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world/indoor.world\n'}
[0.201560] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world/garage.world\n'}
[0.201588] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world/test_world.world\n'}
[0.201615] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world/campus.world\n'}
[0.201643] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world/forest.world\n'}
[0.201670] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world/tunnel.world\n'}
[0.201698] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/ament_index/resource_index/package_run_dependencies/vehicle_simulator\n'}
[0.201726] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/ament_index/resource_index/parent_prefix_path/vehicle_simulator\n'}
[0.201754] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/environment/ament_prefix_path.sh\n'}
[0.201782] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/environment/ament_prefix_path.dsv\n'}
[0.201809] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/environment/path.sh\n'}
[0.201836] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/environment/path.dsv\n'}
[0.201864] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/local_setup.bash\n'}
[0.201895] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/local_setup.sh\n'}
[0.201924] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/local_setup.zsh\n'}
[0.201952] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/local_setup.dsv\n'}
[0.201981] (vehicle_simulator) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.dsv\n'}
[0.202011] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/ament_index/resource_index/packages/vehicle_simulator\n'}
[0.202037] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/cmake/vehicle_simulatorConfig.cmake\n'}
[0.202067] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/cmake/vehicle_simulatorConfig-version.cmake\n'}
[0.202093] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.xml\n'}
[0.202119] (terrain_analysis) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.202151] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/lib/terrain_analysis/terrainAnalysis\n'}
[0.202179] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/launch\n'}
[0.202208] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/launch/terrain_analysis.launch\n'}
[0.202236] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/ament_index/resource_index/package_run_dependencies/terrain_analysis\n'}
[0.202263] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/ament_index/resource_index/parent_prefix_path/terrain_analysis\n'}
[0.202290] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.sh\n'}
[0.202316] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.dsv\n'}
[0.202342] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/environment/path.sh\n'}
[0.202369] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/environment/path.dsv\n'}
[0.202395] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/local_setup.bash\n'}
[0.202423] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/local_setup.sh\n'}
[0.202449] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/local_setup.zsh\n'}
[0.202475] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/local_setup.dsv\n'}
[0.202500] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.dsv\n'}
[0.202526] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/ament_index/resource_index/packages/terrain_analysis\n'}
[0.202551] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig.cmake\n'}
[0.202577] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig-version.cmake\n'}
[0.202604] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.xml\n'}
[0.202631] (velodyne_gazebo_plugins) CommandEnded: {'returncode': 0}
[0.202798] (waypoint_example) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.202841] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/lib/waypoint_example/waypointExample\n'}
[0.202870] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/launch\n'}
[0.202899] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/launch/waypoint_example_garage.launch\n'}
[0.202927] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/data\n'}
[0.202953] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/data/waypoints_garage.ply\n'}
[0.203015] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/data/boundary_garage.ply\n'}
[0.203052] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/ament_index/resource_index/package_run_dependencies/waypoint_example\n'}
[0.203083] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/ament_index/resource_index/parent_prefix_path/waypoint_example\n'}
[0.203113] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/environment/ament_prefix_path.sh\n'}
[0.203141] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/environment/ament_prefix_path.dsv\n'}
[0.203169] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/environment/path.sh\n'}
[0.203198] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/environment/path.dsv\n'}
[0.203228] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/local_setup.bash\n'}
[0.203256] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/local_setup.sh\n'}
[0.203285] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/local_setup.zsh\n'}
[0.203312] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/local_setup.dsv\n'}
[0.203340] (waypoint_example) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.dsv\n'}
[0.203369] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/ament_index/resource_index/packages/waypoint_example\n'}
[0.203396] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/cmake/waypoint_exampleConfig.cmake\n'}
[0.203423] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/cmake/waypoint_exampleConfig-version.cmake\n'}
[0.203449] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.xml\n'}
[0.203590] (tare_planner) StdoutLine: {'line': b'[ 78%] Built target grid_world\n'}
[0.208818] (velodyne_gazebo_plugins) JobEnded: {'identifier': 'velodyne_gazebo_plugins', 'rc': 0}
[0.209481] (local_planner) CommandEnded: {'returncode': 0}
[0.215471] (local_planner) JobEnded: {'identifier': 'local_planner', 'rc': 0}
[0.215779] (waypoint_rviz_plugin) CommandEnded: {'returncode': 0}
[0.216581] (waypoint_rviz_plugin) JobProgress: {'identifier': 'waypoint_rviz_plugin', 'progress': 'install'}
[0.216598] (waypoint_rviz_plugin) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin'], 'cwd': '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.216998] (terrain_analysis_ext) CommandEnded: {'returncode': 0}
[0.223796] (terrain_analysis_ext) JobEnded: {'identifier': 'terrain_analysis_ext', 'rc': 0}
[0.224985] (velodyne_simulator) JobStarted: {'identifier': 'velodyne_simulator'}
[0.228486] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.228998] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/plugin_description.xml\n'}
[0.229063] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib/waypoint_rviz_plugin/libwaypoint_rviz_plugin.so\n'}
[0.229102] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/ament_index/resource_index/package_run_dependencies/waypoint_rviz_plugin\n'}
[0.229137] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/ament_index/resource_index/parent_prefix_path/waypoint_rviz_plugin\n'}
[0.229170] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/ament_prefix_path.sh\n'}
[0.229202] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/ament_prefix_path.dsv\n'}
[0.229233] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/path.sh\n'}
[0.229264] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/path.dsv\n'}
[0.229308] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.bash\n'}
[0.229346] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.sh\n'}
[0.229375] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.zsh\n'}
[0.229404] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.dsv\n'}
[0.229433] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv\n'}
[0.229463] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/ament_index/resource_index/packages/waypoint_rviz_plugin\n'}
[0.229492] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/ament_index/resource_index/rviz_common__pluginlib__plugin/waypoint_rviz_plugin\n'}
[0.229521] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.229550] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/waypoint_rviz_pluginConfig.cmake\n'}
[0.229578] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/waypoint_rviz_pluginConfig-version.cmake\n'}
[0.229606] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.xml\n'}
[0.229660] (vehicle_simulator) CommandEnded: {'returncode': 0}
[0.232623] (tare_planner) StdoutLine: {'line': b'[ 84%] Built target viewpoint_manager\n'}
[0.236732] (vehicle_simulator) JobEnded: {'identifier': 'vehicle_simulator', 'rc': 0}
[0.237359] (terrain_analysis) CommandEnded: {'returncode': 0}
[0.243572] (terrain_analysis) JobEnded: {'identifier': 'terrain_analysis', 'rc': 0}
[0.244195] (waypoint_example) CommandEnded: {'returncode': 0}
[0.250409] (waypoint_example) JobEnded: {'identifier': 'waypoint_example', 'rc': 0}
[0.250967] (waypoint_rviz_plugin) CommandEnded: {'returncode': 0}
[0.257518] (waypoint_rviz_plugin) JobEnded: {'identifier': 'waypoint_rviz_plugin', 'rc': 0}
[0.257825] (velodyne_simulator) JobProgress: {'identifier': 'velodyne_simulator', 'progress': 'cmake'}
[0.258083] (velodyne_simulator) JobProgress: {'identifier': 'velodyne_simulator', 'progress': 'build'}
[0.258300] (velodyne_simulator) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/velodyne_simulator', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/velodyne_simulator', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/velodyne_simulator'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.264759] (tare_planner) StdoutLine: {'line': b'[ 89%] Built target local_coverage_planner\n'}
[0.289888] (visualization_tools) StdoutLine: {'line': b'running egg_info\n'}
[0.290332] (visualization_tools) StdoutLine: {'line': b'writing visualization_tools.egg-info/PKG-INFO\n'}
[0.290479] (visualization_tools) StdoutLine: {'line': b'writing dependency_links to visualization_tools.egg-info/dependency_links.txt\n'}
[0.290598] (visualization_tools) StdoutLine: {'line': b'writing top-level names to visualization_tools.egg-info/top_level.txt\n'}
[0.290765] (velodyne_simulator) CommandEnded: {'returncode': 0}
[0.291249] (velodyne_simulator) JobProgress: {'identifier': 'velodyne_simulator', 'progress': 'install'}
[0.291487] (velodyne_simulator) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/velodyne_simulator'], 'cwd': '/home/<USER>/zhaoluye/build/velodyne_simulator', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/velodyne_simulator'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.292142] (visualization_tools) StdoutLine: {'line': b"reading manifest file 'visualization_tools.egg-info/SOURCES.txt'\n"}
[0.292222] (visualization_tools) StdoutLine: {'line': b"writing manifest file 'visualization_tools.egg-info/SOURCES.txt'\n"}
[0.294190] (tare_planner) StdoutLine: {'line': b'[ 94%] Built target sensor_coverage_planner_ground\n'}
[0.300018] (velodyne_simulator) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.300147] (-) TimerEvent: {}
[0.300263] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/ament_index/resource_index/package_run_dependencies/velodyne_simulator\n'}
[0.300356] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/ament_index/resource_index/parent_prefix_path/velodyne_simulator\n'}
[0.300399] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/environment/ament_prefix_path.sh\n'}
[0.300439] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/environment/ament_prefix_path.dsv\n'}
[0.300478] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/environment/path.sh\n'}
[0.300518] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/environment/path.dsv\n'}
[0.300581] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/local_setup.bash\n'}
[0.300657] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/local_setup.sh\n'}
[0.300692] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/local_setup.zsh\n'}
[0.300725] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/local_setup.dsv\n'}
[0.300760] (velodyne_simulator) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.dsv\n'}
[0.300860] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/ament_index/resource_index/packages/velodyne_simulator\n'}
[0.301039] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/cmake/velodyne_simulatorConfig.cmake\n'}
[0.301150] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/cmake/velodyne_simulatorConfig-version.cmake\n'}
[0.301242] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.xml\n'}
[0.302589] (velodyne_simulator) CommandEnded: {'returncode': 0}
[0.309564] (velodyne_simulator) JobEnded: {'identifier': 'velodyne_simulator', 'rc': 0}
[0.311437] (visualization_tools) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_visualization_tools_egg\n'}
[0.322249] (visualization_tools) CommandEnded: {'returncode': 0}
[0.322838] (visualization_tools) JobProgress: {'identifier': 'visualization_tools', 'progress': 'install'}
[0.323138] (visualization_tools) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/visualization_tools'], 'cwd': '/home/<USER>/zhaoluye/build/visualization_tools', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/visualization_tools'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.328059] (tare_planner) StdoutLine: {'line': b'[100%] Built target tare_planner_node\n'}
[0.328748] (visualization_tools) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.328883] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/lib/visualization_tools/visualizationTools\n'}
[0.329011] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/launch\n'}
[0.329131] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/launch/visualization_tools.launch\n'}
[0.329249] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/pythonpath.sh\n'}
[0.329836] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/pythonpath.dsv\n'}
[0.329969] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info\n'}
[0.330101] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/dependency_links.txt\n'}
[0.330161] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/SOURCES.txt\n'}
[0.330201] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/top_level.txt\n'}
[0.330238] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/PKG-INFO\n'}
[0.330274] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools\n'}
[0.330310] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools/__init__.py\n'}
[0.335407] (tare_planner) CommandEnded: {'returncode': 0}
[0.335864] (tare_planner) JobProgress: {'identifier': 'tare_planner', 'progress': 'install'}
[0.336434] (tare_planner) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/tare_planner'], 'cwd': '/home/<USER>/zhaoluye/build/tare_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/zhaoluye/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/tare_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.342210] (tare_planner) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.342340] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/lib/tare_planner/navigationBoundary\n'}
[0.342498] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/lib/tare_planner/tare_planner_node\n'}
[0.342584] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/\n'}
[0.342639] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore.launch\n'}
[0.342724] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_tunnel.launch\n'}
[0.342760] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_garage.launch\n'}
[0.342796] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_matterport.launch\n'}
[0.342830] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_indoor.launch\n'}
[0.342864] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_forest.launch\n'}
[0.342896] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_campus.launch\n'}
[0.342928] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/\n'}
[0.342973] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//tare_planner_ground.rviz\n'}
[0.343014] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner\n'}
[0.343046] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/campus.yaml\n'}
[0.343140] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/tunnel.yaml\n'}
[0.343178] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/garage.yaml\n'}
[0.343211] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/indoor.yaml\n'}
[0.343245] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/matterport.yaml\n'}
[0.343277] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/forest.yaml\n'}
[0.343309] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner\n'}
[0.343362] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/boundary.ply\n'}
[0.343392] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/package_run_dependencies/tare_planner\n'}
[0.343421] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/parent_prefix_path/tare_planner\n'}
[0.343452] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/ament_prefix_path.sh\n'}
[0.343482] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/ament_prefix_path.dsv\n'}
[0.343512] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/path.sh\n'}
[0.343545] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/path.dsv\n'}
[0.343576] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.bash\n'}
[0.343606] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.sh\n'}
[0.343636] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.zsh\n'}
[0.343666] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.dsv\n'}
[0.343696] (tare_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.dsv\n'}
[0.343725] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/packages/tare_planner\n'}
[0.343755] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/cmake/tare_plannerConfig.cmake\n'}
[0.343786] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/cmake/tare_plannerConfig-version.cmake\n'}
[0.343816] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.xml\n'}
[0.344320] (tare_planner) CommandEnded: {'returncode': 0}
[0.351752] (tare_planner) JobEnded: {'identifier': 'tare_planner', 'rc': 0}
[0.353950] (visualization_tools) StdoutLine: {'line': b"Listing '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools'...\n"}
[0.356695] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/lib/visualization_tools/realTimePlot.py\n'}
[0.356749] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/ament_index/resource_index/package_run_dependencies/visualization_tools\n'}
[0.356782] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/ament_index/resource_index/parent_prefix_path/visualization_tools\n'}
[0.356814] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.sh\n'}
[0.356846] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.dsv\n'}
[0.356889] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/path.sh\n'}
[0.356947] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/path.dsv\n'}
[0.356981] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.bash\n'}
[0.357019] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.sh\n'}
[0.357058] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.zsh\n'}
[0.357088] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.dsv\n'}
[0.357118] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.dsv\n'}
[0.357148] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/ament_index/resource_index/packages/visualization_tools\n'}
[0.357196] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig.cmake\n'}
[0.357228] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig-version.cmake\n'}
[0.357257] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.xml\n'}
[0.358378] (visualization_tools) CommandEnded: {'returncode': 0}
[0.364912] (visualization_tools) JobEnded: {'identifier': 'visualization_tools', 'rc': 0}
[0.365338] (-) EventReactorShutdown: {}
