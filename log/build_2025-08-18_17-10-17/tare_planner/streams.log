[0.035s] Invoking command in '/home/<USER>/zhaoluye/build/tare_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/tare_planner -- -j16 -l16
[0.100s] [  5%] Built target misc_utils
[0.106s] [ 10%] Built target graph
[0.106s] [ 21%] Built target lidar_model
[0.106s] [ 21%] Built target pointcloud_utils
[0.107s] [ 26%] Built target tsp_solver
[0.115s] [ 31%] Built target exploration_path
[0.115s] [ 36%] Built target pointcloud_manager
[0.115s] [ 42%] Built target rolling_occupancy_grid
[0.115s] [ 47%] Built target tare_visualizer
[0.116s] [ 52%] Built target navigationBoundary
[0.135s] [ 57%] Built target viewpoint
[0.135s] [ 63%] Built target rolling_grid
[0.138s] [ 68%] Built target planning_env
[0.160s] [ 73%] Built target keypose_graph
[0.188s] [ 78%] Built target grid_world
[0.217s] [ 84%] Built target viewpoint_manager
[0.249s] [ 89%] Built target local_coverage_planner
[0.279s] [ 94%] Built target sensor_coverage_planner_ground
[0.312s] [100%] Built target tare_planner_node
[0.320s] Invoked command in '/home/<USER>/zhaoluye/build/tare_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/tare_planner -- -j16 -l16
[0.321s] Invoking command in '/home/<USER>/zhaoluye/build/tare_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/tare_planner
[0.327s] -- Install configuration: ""
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/lib/tare_planner/navigationBoundary
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/lib/tare_planner/tare_planner_node
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore.launch
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_tunnel.launch
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_garage.launch
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_matterport.launch
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_indoor.launch
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_forest.launch
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_campus.launch
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//tare_planner_ground.rviz
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/campus.yaml
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/tunnel.yaml
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/garage.yaml
[0.327s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/indoor.yaml
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/matterport.yaml
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/forest.yaml
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/boundary.ply
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/package_run_dependencies/tare_planner
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/parent_prefix_path/tare_planner
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/ament_prefix_path.sh
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/ament_prefix_path.dsv
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/path.sh
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/path.dsv
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.bash
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.sh
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.zsh
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.dsv
[0.328s] -- Installing: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.dsv
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/packages/tare_planner
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/cmake/tare_plannerConfig.cmake
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/cmake/tare_plannerConfig-version.cmake
[0.328s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.xml
[0.329s] Invoked command in '/home/<USER>/zhaoluye/build/tare_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/tare_planner
