[0.065s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.066s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x70be22540e50>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x70be22540a00>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x70be22540a00>>)
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.164s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.164s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.164s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.164s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.164s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/zhaoluye'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ros'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['cmake', 'python']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'cmake'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'python'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['python_setup_py']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'python_setup_py'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'ros'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extensions ['cmake', 'python']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'cmake'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'python'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extensions ['python_setup_py']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'python_setup_py'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'ros'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extensions ['cmake', 'python']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'cmake'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'python'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extensions ['python_setup_py']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'python_setup_py'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'ignore_ament_install'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extensions ['colcon_pkg']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'colcon_pkg'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extensions ['colcon_meta']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'colcon_meta'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extensions ['ros']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'ros'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extensions ['cmake', 'python']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'cmake'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'python'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extensions ['python_setup_py']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'python_setup_py'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'ignore_ament_install'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extensions ['colcon_pkg']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'colcon_pkg'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extensions ['colcon_meta']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'colcon_meta'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extensions ['ros']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'ros'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extensions ['cmake', 'python']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'cmake'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'python'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extensions ['python_setup_py']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'python_setup_py'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore_ament_install'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_pkg']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_pkg'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_meta']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_meta'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ros']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ros'
[0.175s] DEBUG:colcon.colcon_core.package_identification:Package 'src/loam_interface' with type 'ros.ament_cmake' and name 'loam_interface'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ignore', 'ignore_ament_install']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore_ament_install'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_pkg']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_pkg'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_meta']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_meta'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ros']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ros'
[0.176s] DEBUG:colcon.colcon_core.package_identification:Package 'src/local_planner' with type 'ros.ament_cmake' and name 'local_planner'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore_ament_install'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_pkg']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_pkg'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_meta']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_meta'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ros']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ros'
[0.177s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sensor_scan_generation' with type 'ros.ament_cmake' and name 'sensor_scan_generation'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extensions ['ignore', 'ignore_ament_install']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extension 'ignore'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extension 'ignore_ament_install'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extensions ['colcon_pkg']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extension 'colcon_pkg'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extensions ['colcon_meta']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extension 'colcon_meta'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extensions ['ros']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extension 'ros'
[0.178s] DEBUG:colcon.colcon_core.package_identification:Package 'src/tare_planner' with type 'ros.ament_cmake' and name 'tare_planner'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ignore', 'ignore_ament_install']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore_ament_install'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_pkg']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_pkg'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_meta']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_meta'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ros']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ros'
[0.180s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis' with type 'ros.ament_cmake' and name 'terrain_analysis'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ignore', 'ignore_ament_install']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore_ament_install'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_pkg']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_pkg'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_meta']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_meta'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ros']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ros'
[0.180s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis_ext' with type 'ros.ament_cmake' and name 'terrain_analysis_ext'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'ignore'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'ignore_ament_install'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extensions ['colcon_pkg']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'colcon_pkg'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extensions ['colcon_meta']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'colcon_meta'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extensions ['ros']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'ros'
[0.182s] DEBUG:colcon.colcon_core.package_identification:Package 'src/vehicle_simulator' with type 'ros.ament_cmake' and name 'vehicle_simulator'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'ignore_ament_install'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['colcon_pkg']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'colcon_pkg'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['colcon_meta']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'colcon_meta'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['ros']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'ros'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['cmake', 'python']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'cmake'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'python'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['python_setup_py']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'python_setup_py'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extensions ['ignore', 'ignore_ament_install']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extension 'ignore_ament_install'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extensions ['colcon_pkg']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extension 'colcon_pkg'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extensions ['colcon_meta']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extension 'colcon_meta'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extensions ['ros']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extension 'ros'
[0.182s] DEBUG:colcon.colcon_core.package_identification:Package 'src/velodyne_simulator/velodyne_description' with type 'ros.ament_cmake' and name 'velodyne_description'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extension 'ignore'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extension 'ignore_ament_install'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extensions ['colcon_pkg']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extension 'colcon_pkg'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extensions ['colcon_meta']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extension 'colcon_meta'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extensions ['ros']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extension 'ros'
[0.183s] DEBUG:colcon.colcon_core.package_identification:Package 'src/velodyne_simulator/velodyne_gazebo_plugins' with type 'ros.ament_cmake' and name 'velodyne_gazebo_plugins'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extension 'ignore'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extension 'ignore_ament_install'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extensions ['colcon_pkg']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extension 'colcon_pkg'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extensions ['colcon_meta']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extension 'colcon_meta'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extensions ['ros']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extension 'ros'
[0.184s] DEBUG:colcon.colcon_core.package_identification:Package 'src/velodyne_simulator/velodyne_simulator' with type 'ros.ament_cmake' and name 'velodyne_simulator'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore_ament_install'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_pkg']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_pkg'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_meta']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_meta'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ros']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ros'
[0.185s] DEBUG:colcon.colcon_core.package_identification:Package 'src/visualization_tools' with type 'ros.ament_cmake' and name 'visualization_tools'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ignore', 'ignore_ament_install']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_meta'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ros']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ros'
[0.186s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_example' with type 'ros.ament_cmake' and name 'waypoint_example'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ignore', 'ignore_ament_install']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore_ament_install'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_pkg']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_pkg'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_meta']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_meta'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ros']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ros'
[0.187s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_rviz_plugin' with type 'ros.ament_cmake' and name 'waypoint_rviz_plugin'
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.206s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.206s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.207s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 13 installed packages in /home/<USER>/zhaoluye/install
[0.208s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 473 installed packages in /opt/ros/humble
[0.210s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.241s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_args' from command line to 'None'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_target' from command line to 'None'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_clean_cache' from command line to 'False'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_clean_first' from command line to 'False'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_force_configure' from command line to 'False'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'ament_cmake_args' from command line to 'None'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'catkin_cmake_args' from command line to 'None'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.242s] DEBUG:colcon.colcon_core.verb:Building package 'loam_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/loam_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/loam_interface', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/loam_interface', 'symlink_install': False, 'test_result_base': None}
[0.242s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_args' from command line to 'None'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target' from command line to 'None'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.242s] DEBUG:colcon.colcon_core.verb:Building package 'local_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/local_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/local_planner', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/local_planner', 'symlink_install': False, 'test_result_base': None}
[0.242s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_args' from command line to 'None'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_target' from command line to 'None'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_clean_cache' from command line to 'False'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_clean_first' from command line to 'False'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_force_configure' from command line to 'False'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'ament_cmake_args' from command line to 'None'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'catkin_cmake_args' from command line to 'None'
[0.242s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.242s] DEBUG:colcon.colcon_core.verb:Building package 'sensor_scan_generation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/sensor_scan_generation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/sensor_scan_generation', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/sensor_scan_generation', 'symlink_install': False, 'test_result_base': None}
[0.242s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_args' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_target' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.243s] DEBUG:colcon.colcon_core.verb:Building package 'tare_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/tare_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/tare_planner', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/tare_planner', 'symlink_install': False, 'test_result_base': None}
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_args' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_target' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_clean_cache' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_clean_first' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_force_configure' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'ament_cmake_args' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'catkin_cmake_args' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.243s] DEBUG:colcon.colcon_core.verb:Building package 'terrain_analysis' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/terrain_analysis', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/terrain_analysis', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/terrain_analysis', 'symlink_install': False, 'test_result_base': None}
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_args' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_target' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_clean_cache' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_clean_first' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_force_configure' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'ament_cmake_args' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'catkin_cmake_args' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.243s] DEBUG:colcon.colcon_core.verb:Building package 'terrain_analysis_ext' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/terrain_analysis_ext', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/terrain_analysis_ext', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/terrain_analysis_ext', 'symlink_install': False, 'test_result_base': None}
[0.243s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'cmake_args' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'cmake_target' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'cmake_clean_cache' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'cmake_clean_first' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'cmake_force_configure' from command line to 'False'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'ament_cmake_args' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'catkin_cmake_args' from command line to 'None'
[0.243s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.244s] DEBUG:colcon.colcon_core.verb:Building package 'vehicle_simulator' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/vehicle_simulator', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/vehicle_simulator', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/vehicle_simulator', 'symlink_install': False, 'test_result_base': None}
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'cmake_args' from command line to 'None'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'cmake_target' from command line to 'None'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'cmake_clean_first' from command line to 'False'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'cmake_force_configure' from command line to 'False'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'ament_cmake_args' from command line to 'None'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.244s] DEBUG:colcon.colcon_core.verb:Building package 'velodyne_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/velodyne_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/velodyne_description', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_description', 'symlink_install': False, 'test_result_base': None}
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'cmake_args' from command line to 'None'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'cmake_target' from command line to 'None'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.244s] DEBUG:colcon.colcon_core.verb:Building package 'velodyne_gazebo_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_gazebo_plugins', 'symlink_install': False, 'test_result_base': None}
[0.244s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_args' from command line to 'None'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_target' from command line to 'None'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_clean_cache' from command line to 'False'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_clean_first' from command line to 'False'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_force_configure' from command line to 'False'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'ament_cmake_args' from command line to 'None'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'catkin_cmake_args' from command line to 'None'
[0.244s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.244s] DEBUG:colcon.colcon_core.verb:Building package 'visualization_tools' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/visualization_tools', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/visualization_tools', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/visualization_tools', 'symlink_install': False, 'test_result_base': None}
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_target' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_clean_cache' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_clean_first' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_force_configure' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'ament_cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'catkin_cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.245s] DEBUG:colcon.colcon_core.verb:Building package 'waypoint_example' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/waypoint_example', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/waypoint_example', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/waypoint_example', 'symlink_install': False, 'test_result_base': None}
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_target' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_clean_cache' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_clean_first' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_force_configure' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'ament_cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'catkin_cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.245s] DEBUG:colcon.colcon_core.verb:Building package 'waypoint_rviz_plugin' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/waypoint_rviz_plugin', 'symlink_install': False, 'test_result_base': None}
[0.245s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'cmake_target' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'cmake_clean_cache' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'cmake_clean_first' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'cmake_force_configure' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'ament_cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'catkin_cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.245s] DEBUG:colcon.colcon_core.verb:Building package 'velodyne_simulator' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/velodyne_simulator', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/velodyne_simulator', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_simulator', 'symlink_install': False, 'test_result_base': None}
[0.245s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.246s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.246s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_description' with build type 'ament_cmake'
[0.247s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_description'
[0.248s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.248s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.248s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.251s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_gazebo_plugins' with build type 'ament_cmake'
[0.251s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_gazebo_plugins'
[0.251s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.251s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.254s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/loam_interface' with build type 'ament_cmake'
[0.254s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/loam_interface'
[0.254s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.254s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.256s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/local_planner' with build type 'ament_cmake'
[0.256s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/local_planner'
[0.256s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.256s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.259s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/sensor_scan_generation' with build type 'ament_cmake'
[0.259s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/sensor_scan_generation'
[0.259s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.259s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.262s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/tare_planner' with build type 'ament_cmake'
[0.262s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/tare_planner'
[0.262s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.262s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.264s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/terrain_analysis' with build type 'ament_cmake'
[0.264s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/terrain_analysis'
[0.264s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.265s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.267s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/terrain_analysis_ext' with build type 'ament_cmake'
[0.267s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/terrain_analysis_ext'
[0.267s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.267s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.270s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/vehicle_simulator' with build type 'ament_cmake'
[0.270s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/vehicle_simulator'
[0.270s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.270s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.272s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/visualization_tools' with build type 'ament_cmake'
[0.272s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/visualization_tools'
[0.273s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.273s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.275s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/waypoint_example' with build type 'ament_cmake'
[0.275s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/waypoint_example'
[0.275s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.275s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.278s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/waypoint_rviz_plugin' with build type 'ament_cmake'
[0.278s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/waypoint_rviz_plugin'
[0.278s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.278s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.285s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/velodyne_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_description -- -j16 -l16
[0.287s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_gazebo_plugins -- -j16 -l16
[0.289s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/loam_interface': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/loam_interface -- -j16 -l16
[0.291s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/local_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/local_planner -- -j16 -l16
[0.294s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/sensor_scan_generation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/sensor_scan_generation -- -j16 -l16
[0.297s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/tare_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/tare_planner -- -j16 -l16
[0.300s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/terrain_analysis': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/terrain_analysis -- -j16 -l16
[0.303s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/terrain_analysis_ext': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/terrain_analysis_ext -- -j16 -l16
[0.309s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/vehicle_simulator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/vehicle_simulator -- -j16 -l16
[0.312s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/visualization_tools': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/visualization_tools -- -j16 -l16
[0.318s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/waypoint_example': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/waypoint_example -- -j16 -l16
[0.323s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/waypoint_rviz_plugin -- -j16 -l16
[0.325s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/velodyne_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_description -- -j16 -l16
[0.336s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/velodyne_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_description
[0.369s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/sensor_scan_generation -- -j16 -l16
[0.378s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/sensor_scan_generation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/sensor_scan_generation
[0.379s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/loam_interface' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/loam_interface -- -j16 -l16
[0.381s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/loam_interface': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/loam_interface
[0.381s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(velodyne_description)
[0.381s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/velodyne_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_description
[0.384s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description' for CMake module files
[0.384s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description' for CMake config files
[0.384s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_description', 'cmake_prefix_path')
[0.385s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/hook/cmake_prefix_path.ps1'
[0.385s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/hook/cmake_prefix_path.dsv'
[0.386s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/hook/cmake_prefix_path.sh'
[0.387s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/bin'
[0.387s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/lib/pkgconfig/velodyne_description.pc'
[0.387s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/lib/python3.10/site-packages'
[0.387s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/bin'
[0.387s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.ps1'
[0.388s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.dsv'
[0.389s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.sh'
[0.389s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.bash'
[0.390s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.zsh'
[0.390s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/velodyne_description/share/colcon-core/packages/velodyne_description)
[0.390s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(velodyne_description)
[0.391s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description' for CMake module files
[0.391s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description' for CMake config files
[0.391s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_description', 'cmake_prefix_path')
[0.391s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/hook/cmake_prefix_path.ps1'
[0.391s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/hook/cmake_prefix_path.dsv'
[0.392s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/hook/cmake_prefix_path.sh'
[0.392s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/bin'
[0.392s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/lib/pkgconfig/velodyne_description.pc'
[0.392s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/lib/python3.10/site-packages'
[0.392s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/bin'
[0.392s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.ps1'
[0.393s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.dsv'
[0.393s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.sh'
[0.393s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.bash'
[0.393s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.zsh'
[0.394s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/velodyne_description/share/colcon-core/packages/velodyne_description)
[0.397s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_gazebo_plugins -- -j16 -l16
[0.399s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_gazebo_plugins
[0.400s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/local_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/local_planner -- -j16 -l16
[0.402s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/local_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/local_planner
[0.403s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/terrain_analysis_ext' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/terrain_analysis_ext -- -j16 -l16
[0.404s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/terrain_analysis_ext': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/terrain_analysis_ext
[0.404s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sensor_scan_generation)
[0.405s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation' for CMake module files
[0.405s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/sensor_scan_generation
[0.406s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation' for CMake config files
[0.406s] Level 1:colcon.colcon_core.shell:create_environment_hook('sensor_scan_generation', 'cmake_prefix_path')
[0.406s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.ps1'
[0.407s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.dsv'
[0.407s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.sh'
[0.408s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/lib'
[0.408s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/bin'
[0.408s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/lib/pkgconfig/sensor_scan_generation.pc'
[0.409s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/lib/python3.10/site-packages'
[0.409s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/bin'
[0.409s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.ps1'
[0.410s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv'
[0.410s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.sh'
[0.411s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.bash'
[0.411s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.zsh'
[0.411s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/sensor_scan_generation/share/colcon-core/packages/sensor_scan_generation)
[0.411s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sensor_scan_generation)
[0.412s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation' for CMake module files
[0.412s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation' for CMake config files
[0.412s] Level 1:colcon.colcon_core.shell:create_environment_hook('sensor_scan_generation', 'cmake_prefix_path')
[0.412s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.ps1'
[0.413s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.dsv'
[0.413s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.sh'
[0.413s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/lib'
[0.413s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/bin'
[0.413s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/lib/pkgconfig/sensor_scan_generation.pc'
[0.413s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/lib/python3.10/site-packages'
[0.414s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/bin'
[0.414s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.ps1'
[0.414s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv'
[0.414s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.sh'
[0.415s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.bash'
[0.415s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.zsh'
[0.415s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/sensor_scan_generation/share/colcon-core/packages/sensor_scan_generation)
[0.415s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(loam_interface)
[0.415s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface' for CMake module files
[0.416s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface' for CMake config files
[0.416s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/loam_interface' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/loam_interface
[0.416s] Level 1:colcon.colcon_core.shell:create_environment_hook('loam_interface', 'cmake_prefix_path')
[0.417s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.ps1'
[0.417s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.dsv'
[0.417s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.sh'
[0.418s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/lib'
[0.418s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/bin'
[0.418s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/lib/pkgconfig/loam_interface.pc'
[0.418s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/lib/python3.10/site-packages'
[0.418s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/bin'
[0.419s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.ps1'
[0.419s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.dsv'
[0.420s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.sh'
[0.420s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.bash'
[0.420s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.zsh'
[0.421s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/loam_interface/share/colcon-core/packages/loam_interface)
[0.421s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(loam_interface)
[0.422s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface' for CMake module files
[0.422s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface' for CMake config files
[0.422s] Level 1:colcon.colcon_core.shell:create_environment_hook('loam_interface', 'cmake_prefix_path')
[0.422s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.ps1'
[0.422s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.dsv'
[0.423s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.sh'
[0.423s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/lib'
[0.423s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/bin'
[0.423s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/lib/pkgconfig/loam_interface.pc'
[0.423s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/lib/python3.10/site-packages'
[0.423s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/bin'
[0.424s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.ps1'
[0.424s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.dsv'
[0.424s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.sh'
[0.424s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.bash'
[0.424s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.zsh'
[0.425s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/loam_interface/share/colcon-core/packages/loam_interface)
[0.426s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/vehicle_simulator' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/vehicle_simulator -- -j16 -l16
[0.427s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/vehicle_simulator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/vehicle_simulator
[0.428s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/terrain_analysis -- -j16 -l16
[0.430s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/terrain_analysis': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/terrain_analysis
[0.436s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(velodyne_gazebo_plugins)
[0.436s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins' for CMake module files
[0.437s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins' for CMake config files
[0.438s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_gazebo_plugins', 'cmake_prefix_path')
[0.438s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/cmake_prefix_path.ps1'
[0.439s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/cmake_prefix_path.dsv'
[0.439s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/cmake_prefix_path.sh'
[0.440s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/waypoint_example' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/waypoint_example -- -j16 -l16
[0.440s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib'
[0.440s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_gazebo_plugins', 'ld_library_path_lib')
[0.440s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/ld_library_path_lib.ps1'
[0.441s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/ld_library_path_lib.dsv'
[0.441s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/ld_library_path_lib.sh'
[0.442s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/bin'
[0.443s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib/pkgconfig/velodyne_gazebo_plugins.pc'
[0.443s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib/python3.10/site-packages'
[0.443s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/bin'
[0.443s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.ps1'
[0.445s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.dsv'
[0.446s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/waypoint_example': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/waypoint_example
[0.447s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.sh'
[0.449s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_gazebo_plugins
[0.450s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.bash'
[0.450s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.zsh'
[0.450s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/colcon-core/packages/velodyne_gazebo_plugins)
[0.451s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(velodyne_gazebo_plugins)
[0.451s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins' for CMake module files
[0.451s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins' for CMake config files
[0.451s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_gazebo_plugins', 'cmake_prefix_path')
[0.451s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/cmake_prefix_path.ps1'
[0.452s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/cmake_prefix_path.dsv'
[0.452s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/cmake_prefix_path.sh'
[0.452s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib'
[0.452s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_gazebo_plugins', 'ld_library_path_lib')
[0.452s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/ld_library_path_lib.ps1'
[0.453s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/ld_library_path_lib.dsv'
[0.453s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/ld_library_path_lib.sh'
[0.453s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/bin'
[0.453s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib/pkgconfig/velodyne_gazebo_plugins.pc'
[0.453s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib/python3.10/site-packages'
[0.453s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/bin'
[0.454s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.ps1'
[0.454s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.dsv'
[0.454s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.sh'
[0.454s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.bash'
[0.455s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.zsh'
[0.455s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/colcon-core/packages/velodyne_gazebo_plugins)
[0.455s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[0.455s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner' for CMake module files
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner' for CMake config files
[0.456s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/local_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/local_planner
[0.456s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[0.456s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[0.456s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[0.456s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[0.457s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/lib'
[0.457s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/bin'
[0.457s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/lib/pkgconfig/local_planner.pc'
[0.457s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/lib/python3.10/site-packages'
[0.457s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/bin'
[0.457s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.ps1'
[0.458s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.dsv'
[0.458s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.sh'
[0.458s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.bash'
[0.458s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.zsh'
[0.458s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/local_planner/share/colcon-core/packages/local_planner)
[0.459s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner' for CMake module files
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner' for CMake config files
[0.459s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[0.459s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[0.459s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[0.460s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[0.460s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/lib'
[0.460s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/bin'
[0.460s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/lib/pkgconfig/local_planner.pc'
[0.460s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/lib/python3.10/site-packages'
[0.460s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/bin'
[0.460s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.ps1'
[0.461s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.dsv'
[0.461s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.sh'
[0.461s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.bash'
[0.461s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.zsh'
[0.461s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/local_planner/share/colcon-core/packages/local_planner)
[0.463s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/waypoint_rviz_plugin -- -j16 -l16
[0.463s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/waypoint_rviz_plugin
[0.463s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis_ext)
[0.463s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext' for CMake module files
[0.463s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/terrain_analysis_ext' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/terrain_analysis_ext
[0.464s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext' for CMake config files
[0.464s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis_ext', 'cmake_prefix_path')
[0.464s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.ps1'
[0.464s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.dsv'
[0.464s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.sh'
[0.465s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/lib'
[0.465s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/bin'
[0.465s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/lib/pkgconfig/terrain_analysis_ext.pc'
[0.465s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/lib/python3.10/site-packages'
[0.465s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/bin'
[0.465s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.ps1'
[0.466s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv'
[0.466s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.sh'
[0.466s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.bash'
[0.466s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.zsh'
[0.466s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/colcon-core/packages/terrain_analysis_ext)
[0.467s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis_ext)
[0.467s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext' for CMake module files
[0.467s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext' for CMake config files
[0.467s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis_ext', 'cmake_prefix_path')
[0.467s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.ps1'
[0.468s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.dsv'
[0.468s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.sh'
[0.468s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/lib'
[0.468s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/bin'
[0.468s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/lib/pkgconfig/terrain_analysis_ext.pc'
[0.468s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/lib/python3.10/site-packages'
[0.468s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/bin'
[0.469s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.ps1'
[0.469s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv'
[0.469s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.sh'
[0.469s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.bash'
[0.470s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.zsh'
[0.470s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/colcon-core/packages/terrain_analysis_ext)
[0.470s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_simulator' with build type 'ament_cmake'
[0.471s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_simulator'
[0.471s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.471s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.475s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(vehicle_simulator)
[0.475s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator' for CMake module files
[0.476s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/vehicle_simulator' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/vehicle_simulator
[0.476s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator' for CMake config files
[0.477s] Level 1:colcon.colcon_core.shell:create_environment_hook('vehicle_simulator', 'cmake_prefix_path')
[0.477s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/hook/cmake_prefix_path.ps1'
[0.477s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/hook/cmake_prefix_path.dsv'
[0.477s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/hook/cmake_prefix_path.sh'
[0.478s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/lib'
[0.478s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/bin'
[0.478s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/lib/pkgconfig/vehicle_simulator.pc'
[0.478s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/lib/python3.10/site-packages'
[0.478s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/bin'
[0.478s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.ps1'
[0.479s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.dsv'
[0.479s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.sh'
[0.479s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.bash'
[0.479s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.zsh'
[0.479s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/vehicle_simulator/share/colcon-core/packages/vehicle_simulator)
[0.480s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(vehicle_simulator)
[0.480s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator' for CMake module files
[0.480s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator' for CMake config files
[0.480s] Level 1:colcon.colcon_core.shell:create_environment_hook('vehicle_simulator', 'cmake_prefix_path')
[0.480s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/hook/cmake_prefix_path.ps1'
[0.481s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/hook/cmake_prefix_path.dsv'
[0.481s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/hook/cmake_prefix_path.sh'
[0.481s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/lib'
[0.481s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/bin'
[0.481s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/lib/pkgconfig/vehicle_simulator.pc'
[0.481s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/lib/python3.10/site-packages'
[0.481s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/bin'
[0.482s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.ps1'
[0.482s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.dsv'
[0.482s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.sh'
[0.482s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.bash'
[0.483s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.zsh'
[0.483s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/vehicle_simulator/share/colcon-core/packages/vehicle_simulator)
[0.483s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis)
[0.483s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis' for CMake module files
[0.484s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/terrain_analysis
[0.484s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis' for CMake config files
[0.484s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis', 'cmake_prefix_path')
[0.484s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.ps1'
[0.484s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.dsv'
[0.484s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.sh'
[0.485s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/lib'
[0.485s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/bin'
[0.485s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/lib/pkgconfig/terrain_analysis.pc'
[0.485s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/lib/python3.10/site-packages'
[0.485s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/bin'
[0.485s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.ps1'
[0.486s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.dsv'
[0.486s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.sh'
[0.486s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.bash'
[0.486s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.zsh'
[0.486s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/terrain_analysis/share/colcon-core/packages/terrain_analysis)
[0.487s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis)
[0.487s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis' for CMake module files
[0.487s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis' for CMake config files
[0.487s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis', 'cmake_prefix_path')
[0.487s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.ps1'
[0.487s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.dsv'
[0.488s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.sh'
[0.488s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/lib'
[0.488s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/bin'
[0.488s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/lib/pkgconfig/terrain_analysis.pc'
[0.488s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/lib/python3.10/site-packages'
[0.488s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/bin'
[0.488s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.ps1'
[0.489s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.dsv'
[0.489s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.sh'
[0.489s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.bash'
[0.489s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.zsh'
[0.490s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/terrain_analysis/share/colcon-core/packages/terrain_analysis)
[0.490s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_example)
[0.490s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example' for CMake module files
[0.491s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/waypoint_example' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/waypoint_example
[0.491s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example' for CMake config files
[0.491s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_example', 'cmake_prefix_path')
[0.491s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.ps1'
[0.491s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.dsv'
[0.491s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.sh'
[0.492s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/lib'
[0.492s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/bin'
[0.492s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/lib/pkgconfig/waypoint_example.pc'
[0.492s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/lib/python3.10/site-packages'
[0.492s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/bin'
[0.492s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.ps1'
[0.492s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.dsv'
[0.493s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.sh'
[0.493s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.bash'
[0.493s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.zsh'
[0.493s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/waypoint_example/share/colcon-core/packages/waypoint_example)
[0.493s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_example)
[0.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example' for CMake module files
[0.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example' for CMake config files
[0.494s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_example', 'cmake_prefix_path')
[0.494s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.ps1'
[0.494s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.dsv'
[0.494s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.sh'
[0.495s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/lib'
[0.495s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/bin'
[0.495s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/lib/pkgconfig/waypoint_example.pc'
[0.495s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/lib/python3.10/site-packages'
[0.495s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/bin'
[0.495s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.ps1'
[0.496s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.dsv'
[0.496s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.sh'
[0.496s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.bash'
[0.496s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.zsh'
[0.496s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/waypoint_example/share/colcon-core/packages/waypoint_example)
[0.497s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_rviz_plugin)
[0.497s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin' for CMake module files
[0.497s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/waypoint_rviz_plugin
[0.497s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin' for CMake config files
[0.498s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_rviz_plugin', 'cmake_prefix_path')
[0.498s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.ps1'
[0.498s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.dsv'
[0.498s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.sh'
[0.498s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib'
[0.498s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/bin'
[0.498s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib/pkgconfig/waypoint_rviz_plugin.pc'
[0.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib/python3.10/site-packages'
[0.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/bin'
[0.499s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.ps1'
[0.499s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv'
[0.499s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.sh'
[0.500s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.bash'
[0.500s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.zsh'
[0.500s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/colcon-core/packages/waypoint_rviz_plugin)
[0.500s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_rviz_plugin)
[0.500s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin' for CMake module files
[0.501s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin' for CMake config files
[0.501s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_rviz_plugin', 'cmake_prefix_path')
[0.501s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.ps1'
[0.501s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.dsv'
[0.501s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.sh'
[0.501s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib'
[0.502s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/bin'
[0.502s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib/pkgconfig/waypoint_rviz_plugin.pc'
[0.502s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib/python3.10/site-packages'
[0.502s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/bin'
[0.502s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.ps1'
[0.502s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv'
[0.503s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.sh'
[0.503s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.bash'
[0.503s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.zsh'
[0.503s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/colcon-core/packages/waypoint_rviz_plugin)
[0.505s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/velodyne_simulator': CMAKE_PREFIX_PATH=/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_simulator -- -j16 -l16
[0.537s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/velodyne_simulator' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_simulator -- -j16 -l16
[0.538s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/velodyne_simulator': CMAKE_PREFIX_PATH=/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_simulator
[0.549s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(velodyne_simulator)
[0.549s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator' for CMake module files
[0.549s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/velodyne_simulator' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_simulator
[0.549s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator' for CMake config files
[0.550s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_simulator', 'cmake_prefix_path')
[0.550s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/hook/cmake_prefix_path.ps1'
[0.550s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/hook/cmake_prefix_path.dsv'
[0.550s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/hook/cmake_prefix_path.sh'
[0.551s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/bin'
[0.551s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/lib/pkgconfig/velodyne_simulator.pc'
[0.551s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/lib/python3.10/site-packages'
[0.551s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/bin'
[0.551s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.ps1'
[0.552s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.dsv'
[0.552s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.sh'
[0.552s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.bash'
[0.552s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.zsh'
[0.552s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/velodyne_simulator/share/colcon-core/packages/velodyne_simulator)
[0.553s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(velodyne_simulator)
[0.553s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator' for CMake module files
[0.553s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator' for CMake config files
[0.553s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_simulator', 'cmake_prefix_path')
[0.553s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/hook/cmake_prefix_path.ps1'
[0.553s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/hook/cmake_prefix_path.dsv'
[0.554s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/hook/cmake_prefix_path.sh'
[0.554s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/bin'
[0.554s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/lib/pkgconfig/velodyne_simulator.pc'
[0.554s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/lib/python3.10/site-packages'
[0.554s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/bin'
[0.554s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.ps1'
[0.555s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.dsv'
[0.555s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.sh'
[0.555s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.bash'
[0.555s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.zsh'
[0.556s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/velodyne_simulator/share/colcon-core/packages/velodyne_simulator)
[0.569s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/visualization_tools -- -j16 -l16
[0.570s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/visualization_tools': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/visualization_tools
[0.582s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/tare_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/tare_planner -- -j16 -l16
[0.583s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/tare_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/tare_planner
[0.590s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(tare_planner)
[0.591s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner' for CMake module files
[0.591s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/tare_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/tare_planner
[0.591s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner' for CMake config files
[0.591s] Level 1:colcon.colcon_core.shell:create_environment_hook('tare_planner', 'cmake_prefix_path')
[0.592s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/hook/cmake_prefix_path.ps1'
[0.592s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/hook/cmake_prefix_path.dsv'
[0.592s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/hook/cmake_prefix_path.sh'
[0.593s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/lib'
[0.593s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/bin'
[0.593s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/lib/pkgconfig/tare_planner.pc'
[0.593s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/lib/python3.10/site-packages'
[0.593s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/bin'
[0.593s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.ps1'
[0.593s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.dsv'
[0.594s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.sh'
[0.594s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.bash'
[0.594s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.zsh'
[0.594s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/tare_planner/share/colcon-core/packages/tare_planner)
[0.595s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(tare_planner)
[0.595s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner' for CMake module files
[0.595s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner' for CMake config files
[0.595s] Level 1:colcon.colcon_core.shell:create_environment_hook('tare_planner', 'cmake_prefix_path')
[0.595s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/hook/cmake_prefix_path.ps1'
[0.596s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/hook/cmake_prefix_path.dsv'
[0.596s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/hook/cmake_prefix_path.sh'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/lib'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/bin'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/lib/pkgconfig/tare_planner.pc'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/lib/python3.10/site-packages'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/bin'
[0.597s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.ps1'
[0.597s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.dsv'
[0.597s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.sh'
[0.597s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.bash'
[0.598s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.zsh'
[0.598s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/tare_planner/share/colcon-core/packages/tare_planner)
[0.605s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(visualization_tools)
[0.605s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools' for CMake module files
[0.605s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/visualization_tools
[0.605s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools' for CMake config files
[0.605s] Level 1:colcon.colcon_core.shell:create_environment_hook('visualization_tools', 'cmake_prefix_path')
[0.605s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.ps1'
[0.606s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.dsv'
[0.606s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.sh'
[0.606s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/lib'
[0.606s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/bin'
[0.606s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/lib/pkgconfig/visualization_tools.pc'
[0.606s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/lib/python3.10/site-packages'
[0.606s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/bin'
[0.607s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.ps1'
[0.607s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.dsv'
[0.607s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.sh'
[0.607s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.bash'
[0.608s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.zsh'
[0.608s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/visualization_tools/share/colcon-core/packages/visualization_tools)
[0.608s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(visualization_tools)
[0.608s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools' for CMake module files
[0.608s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools' for CMake config files
[0.608s] Level 1:colcon.colcon_core.shell:create_environment_hook('visualization_tools', 'cmake_prefix_path')
[0.609s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.ps1'
[0.609s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.dsv'
[0.609s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.sh'
[0.609s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/lib'
[0.609s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/bin'
[0.609s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/lib/pkgconfig/visualization_tools.pc'
[0.609s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/lib/python3.10/site-packages'
[0.610s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/bin'
[0.610s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.ps1'
[0.610s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.dsv'
[0.610s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.sh'
[0.611s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.bash'
[0.611s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.zsh'
[0.611s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/visualization_tools/share/colcon-core/packages/visualization_tools)
[0.611s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.611s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.611s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.611s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.615s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.615s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.615s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.625s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.625s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/zhaoluye/install/local_setup.ps1'
[0.626s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/zhaoluye/install/_local_setup_util_ps1.py'
[0.626s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/zhaoluye/install/setup.ps1'
[0.628s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/zhaoluye/install/local_setup.sh'
[0.628s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/zhaoluye/install/_local_setup_util_sh.py'
[0.628s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/zhaoluye/install/setup.sh'
[0.629s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/zhaoluye/install/local_setup.bash'
[0.629s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/zhaoluye/install/setup.bash'
[0.630s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/zhaoluye/install/local_setup.zsh'
[0.630s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/zhaoluye/install/setup.zsh'
