[0.038s] Invoking command in '/home/<USER>/zhaoluye/build/velodyne_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_description -- -j16 -l16
[0.078s] Invoked command in '/home/<USER>/zhaoluye/build/velodyne_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_description -- -j16 -l16
[0.089s] Invoking command in '/home/<USER>/zhaoluye/build/velodyne_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_description
[0.121s] -- Install configuration: ""
[0.123s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/launch
[0.124s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/launch/example.launch.py
[0.124s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes
[0.124s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/HDL32E_scan.stl
[0.124s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_scan.stl
[0.124s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_scan.dae
[0.124s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/HDL32E_scan.dae
[0.124s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_base_2.stl
[0.124s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/HDL32E_base.dae
[0.124s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/HDL32E_base.stl
[0.124s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_base_1.dae
[0.124s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_base_1.stl
[0.125s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_base_2.dae
[0.125s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/rviz
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/rviz/example.rviz
[0.127s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/urdf
[0.127s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/urdf/example.urdf.xacro
[0.127s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/urdf/VLP-16.urdf.xacro
[0.127s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/urdf/HDL-32E.urdf.xacro
[0.127s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/world
[0.127s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/world/example.world
[0.127s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/velodyne_description.dsv
[0.127s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/ament_index/resource_index/package_run_dependencies/velodyne_description
[0.127s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/ament_index/resource_index/parent_prefix_path/velodyne_description
[0.129s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/ament_prefix_path.sh
[0.129s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/ament_prefix_path.dsv
[0.129s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/path.sh
[0.129s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/path.dsv
[0.129s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/local_setup.bash
[0.129s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/local_setup.sh
[0.129s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/local_setup.zsh
[0.129s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/local_setup.dsv
[0.129s] -- Installing: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.dsv
[0.129s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/ament_index/resource_index/packages/velodyne_description
[0.130s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/cmake/velodyne_descriptionConfig.cmake
[0.130s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/cmake/velodyne_descriptionConfig-version.cmake
[0.130s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.xml
[0.134s] Invoked command in '/home/<USER>/zhaoluye/build/velodyne_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_description
