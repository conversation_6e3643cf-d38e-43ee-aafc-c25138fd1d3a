[0.044s] Invoking command in '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/waypoint_rviz_plugin -- -j16 -l16
[0.099s] [ 16%] [34m[1mAutomatic MOC for target waypoint_rviz_plugin[0m
[0.107s] [ 16%] Built target waypoint_rviz_plugin_autogen
[0.134s] [100%] Built target waypoint_rviz_plugin
[0.184s] Invoked command in '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/waypoint_rviz_plugin -- -j16 -l16
[0.185s] Invoking command in '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/waypoint_rviz_plugin
[0.197s] -- Install configuration: ""
[0.197s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/plugin_description.xml
[0.197s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib/waypoint_rviz_plugin/libwaypoint_rviz_plugin.so
[0.197s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/ament_index/resource_index/package_run_dependencies/waypoint_rviz_plugin
[0.197s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/ament_index/resource_index/parent_prefix_path/waypoint_rviz_plugin
[0.197s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/ament_prefix_path.sh
[0.197s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/ament_prefix_path.dsv
[0.197s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/path.sh
[0.197s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/path.dsv
[0.197s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.bash
[0.197s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.sh
[0.197s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.zsh
[0.197s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.dsv
[0.197s] -- Installing: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv
[0.198s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/ament_index/resource_index/packages/waypoint_rviz_plugin
[0.198s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/ament_index/resource_index/rviz_common__pluginlib__plugin/waypoint_rviz_plugin
[0.198s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/ament_cmake_export_dependencies-extras.cmake
[0.198s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/waypoint_rviz_pluginConfig.cmake
[0.198s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/waypoint_rviz_pluginConfig-version.cmake
[0.198s] -- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.xml
[0.219s] Invoked command in '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/waypoint_rviz_plugin
