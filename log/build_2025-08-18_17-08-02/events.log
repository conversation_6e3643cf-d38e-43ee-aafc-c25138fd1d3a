[0.000000] (-) TimerEvent: {}
[0.000552] (loam_interface) JobQueued: {'identifier': 'loam_interface', 'dependencies': OrderedDict()}
[0.000591] (local_planner) JobQueued: {'identifier': 'local_planner', 'dependencies': OrderedDict()}
[0.000619] (sensor_scan_generation) JobQueued: {'identifier': 'sensor_scan_generation', 'dependencies': OrderedDict()}
[0.000644] (tare_planner) JobQueued: {'identifier': 'tare_planner', 'dependencies': OrderedDict()}
[0.000668] (terrain_analysis) JobQueued: {'identifier': 'terrain_analysis', 'dependencies': OrderedDict()}
[0.000692] (terrain_analysis_ext) JobQueued: {'identifier': 'terrain_analysis_ext', 'dependencies': OrderedDict()}
[0.000715] (vehicle_simulator) JobQueued: {'identifier': 'vehicle_simulator', 'dependencies': OrderedDict()}
[0.000739] (velodyne_description) JobQueued: {'identifier': 'velodyne_description', 'dependencies': OrderedDict()}
[0.000761] (velodyne_gazebo_plugins) JobQueued: {'identifier': 'velodyne_gazebo_plugins', 'dependencies': OrderedDict()}
[0.000790] (visualization_tools) JobQueued: {'identifier': 'visualization_tools', 'dependencies': OrderedDict()}
[0.000816] (waypoint_example) JobQueued: {'identifier': 'waypoint_example', 'dependencies': OrderedDict()}
[0.000839] (waypoint_rviz_plugin) JobQueued: {'identifier': 'waypoint_rviz_plugin', 'dependencies': OrderedDict()}
[0.000862] (velodyne_simulator) JobQueued: {'identifier': 'velodyne_simulator', 'dependencies': OrderedDict([('velodyne_description', '/home/<USER>/zhaoluye/install/velodyne_description'), ('velodyne_gazebo_plugins', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins')])}
[0.000890] (velodyne_description) JobStarted: {'identifier': 'velodyne_description'}
[0.006359] (velodyne_gazebo_plugins) JobStarted: {'identifier': 'velodyne_gazebo_plugins'}
[0.008816] (loam_interface) JobStarted: {'identifier': 'loam_interface'}
[0.011435] (local_planner) JobStarted: {'identifier': 'local_planner'}
[0.014203] (sensor_scan_generation) JobStarted: {'identifier': 'sensor_scan_generation'}
[0.016875] (tare_planner) JobStarted: {'identifier': 'tare_planner'}
[0.019597] (terrain_analysis) JobStarted: {'identifier': 'terrain_analysis'}
[0.022389] (terrain_analysis_ext) JobStarted: {'identifier': 'terrain_analysis_ext'}
[0.025031] (vehicle_simulator) JobStarted: {'identifier': 'vehicle_simulator'}
[0.027646] (visualization_tools) JobStarted: {'identifier': 'visualization_tools'}
[0.030557] (waypoint_example) JobStarted: {'identifier': 'waypoint_example'}
[0.033250] (waypoint_rviz_plugin) JobStarted: {'identifier': 'waypoint_rviz_plugin'}
[0.037893] (velodyne_description) JobProgress: {'identifier': 'velodyne_description', 'progress': 'cmake'}
[0.038492] (velodyne_description) JobProgress: {'identifier': 'velodyne_description', 'progress': 'build'}
[0.038882] (velodyne_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/velodyne_description', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/velodyne_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/velodyne_description'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.040499] (velodyne_gazebo_plugins) JobProgress: {'identifier': 'velodyne_gazebo_plugins', 'progress': 'cmake'}
[0.041207] (velodyne_gazebo_plugins) JobProgress: {'identifier': 'velodyne_gazebo_plugins', 'progress': 'build'}
[0.041762] (velodyne_gazebo_plugins) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.043086] (loam_interface) JobProgress: {'identifier': 'loam_interface', 'progress': 'cmake'}
[0.043366] (loam_interface) JobProgress: {'identifier': 'loam_interface', 'progress': 'build'}
[0.043622] (loam_interface) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/loam_interface', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/loam_interface', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/loam_interface'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.044583] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'cmake'}
[0.045277] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'build'}
[0.045925] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/local_planner', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/local_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.048054] (sensor_scan_generation) JobProgress: {'identifier': 'sensor_scan_generation', 'progress': 'cmake'}
[0.048474] (sensor_scan_generation) JobProgress: {'identifier': 'sensor_scan_generation', 'progress': 'build'}
[0.049389] (sensor_scan_generation) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/sensor_scan_generation', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/sensor_scan_generation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/sensor_scan_generation'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.050805] (tare_planner) JobProgress: {'identifier': 'tare_planner', 'progress': 'cmake'}
[0.051349] (tare_planner) JobProgress: {'identifier': 'tare_planner', 'progress': 'build'}
[0.052172] (tare_planner) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/tare_planner', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/tare_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/tare_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.053706] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'cmake'}
[0.054615] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'build'}
[0.055435] (terrain_analysis) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/terrain_analysis', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/terrain_analysis', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/terrain_analysis'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.057096] (terrain_analysis_ext) JobProgress: {'identifier': 'terrain_analysis_ext', 'progress': 'cmake'}
[0.057614] (terrain_analysis_ext) JobProgress: {'identifier': 'terrain_analysis_ext', 'progress': 'build'}
[0.058237] (terrain_analysis_ext) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/terrain_analysis_ext', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/terrain_analysis_ext', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/terrain_analysis_ext'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.060032] (vehicle_simulator) JobProgress: {'identifier': 'vehicle_simulator', 'progress': 'cmake'}
[0.061402] (vehicle_simulator) JobProgress: {'identifier': 'vehicle_simulator', 'progress': 'build'}
[0.062286] (vehicle_simulator) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/vehicle_simulator', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/vehicle_simulator', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/vehicle_simulator'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.064408] (visualization_tools) JobProgress: {'identifier': 'visualization_tools', 'progress': 'cmake'}
[0.065222] (visualization_tools) JobProgress: {'identifier': 'visualization_tools', 'progress': 'build'}
[0.065984] (visualization_tools) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/visualization_tools', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/visualization_tools', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/visualization_tools'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.067699] (waypoint_example) JobProgress: {'identifier': 'waypoint_example', 'progress': 'cmake'}
[0.068899] (waypoint_example) JobProgress: {'identifier': 'waypoint_example', 'progress': 'build'}
[0.069574] (waypoint_example) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/waypoint_example', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/waypoint_example', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/waypoint_example'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.073649] (velodyne_gazebo_plugins) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target gazebo_ros_velodyne_laser\x1b[0m\n'}
[0.075701] (waypoint_rviz_plugin) JobProgress: {'identifier': 'waypoint_rviz_plugin', 'progress': 'cmake'}
[0.076461] (waypoint_rviz_plugin) JobProgress: {'identifier': 'waypoint_rviz_plugin', 'progress': 'build'}
[0.076992] (waypoint_rviz_plugin) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.078713] (velodyne_description) CommandEnded: {'returncode': 0}
[0.079894] (velodyne_description) JobProgress: {'identifier': 'velodyne_description', 'progress': 'install'}
[0.080246] (loam_interface) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target loamInterface\x1b[0m\n'}
[0.082636] (sensor_scan_generation) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target sensorScanGeneration\x1b[0m\n'}
[0.082819] (local_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target pathFollower\x1b[0m\n'}
[0.083478] (local_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target localPlanner\x1b[0m\n'}
[0.088776] (velodyne_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/velodyne_description'], 'cwd': '/home/<USER>/zhaoluye/build/velodyne_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/velodyne_description'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.099966] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target lidar_model\x1b[0m\n'}
[0.100257] (-) TimerEvent: {}
[0.100439] (terrain_analysis) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target terrainAnalysis\x1b[0m\n'}
[0.100552] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target pointcloud_utils\x1b[0m\n'}
[0.100608] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target tare_visualizer\x1b[0m\n'}
[0.101071] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target exploration_path\x1b[0m\n'}
[0.101141] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target pointcloud_manager\x1b[0m\n'}
[0.102106] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target graph\x1b[0m\n'}
[0.103249] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target navigationBoundary\x1b[0m\n'}
[0.103374] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target misc_utils\x1b[0m\n'}
[0.107761] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target tsp_solver\x1b[0m\n'}
[0.109232] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rolling_occupancy_grid\x1b[0m\n'}
[0.111712] (velodyne_gazebo_plugins) StdoutLine: {'line': b'[100%] Built target gazebo_ros_velodyne_laser\n'}
[0.114830] (loam_interface) StdoutLine: {'line': b'[100%] Built target loamInterface\n'}
[0.115095] (terrain_analysis_ext) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target terrainAnalysisExt\x1b[0m\n'}
[0.115293] (sensor_scan_generation) StdoutLine: {'line': b'[100%] Built target sensorScanGeneration\n'}
[0.115909] (local_planner) StdoutLine: {'line': b'[ 50%] Built target pathFollower\n'}
[0.120616] (terrain_analysis) StdoutLine: {'line': b'[100%] Built target terrainAnalysis\n'}
[0.121091] (local_planner) StdoutLine: {'line': b'[100%] Built target localPlanner\n'}
[0.121564] (visualization_tools) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target visualizationTools\x1b[0m\n'}
[0.125429] (sensor_scan_generation) CommandEnded: {'returncode': 0}
[0.130110] (sensor_scan_generation) JobProgress: {'identifier': 'sensor_scan_generation', 'progress': 'install'}
[0.130251] (sensor_scan_generation) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/sensor_scan_generation'], 'cwd': '/home/<USER>/zhaoluye/build/sensor_scan_generation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/sensor_scan_generation'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.130998] (waypoint_example) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target waypointExample\x1b[0m\n'}
[0.131130] (vehicle_simulator) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target vehicleSimulator\x1b[0m\n'}
[0.131201] (tare_planner) StdoutLine: {'line': b'[  5%] Built target pointcloud_manager\n'}
[0.131269] (tare_planner) StdoutLine: {'line': b'[ 10%] Built target exploration_path\n'}
[0.131363] (tare_planner) StdoutLine: {'line': b'[ 15%] Built target pointcloud_utils\n'}
[0.131409] (tare_planner) StdoutLine: {'line': b'[ 21%] Built target tsp_solver\n'}
[0.132540] (tare_planner) StdoutLine: {'line': b'[ 26%] Built target graph\n'}
[0.132662] (velodyne_description) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.132730] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/launch\n'}
[0.132767] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/launch/example.launch.py\n'}
[0.132810] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes\n'}
[0.132840] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/HDL32E_scan.stl\n'}
[0.132869] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_scan.stl\n'}
[0.132899] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_scan.dae\n'}
[0.132928] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/HDL32E_scan.dae\n'}
[0.132955] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_base_2.stl\n'}
[0.132983] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/HDL32E_base.dae\n'}
[0.133009] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/HDL32E_base.stl\n'}
[0.133872] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_base_1.dae\n'}
[0.133963] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_base_1.stl\n'}
[0.134014] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/meshes/VLP16_base_2.dae\n'}
[0.134063] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/rviz\n'}
[0.134141] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/rviz/example.rviz\n'}
[0.134195] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/urdf\n'}
[0.134245] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/urdf/example.urdf.xacro\n'}
[0.134701] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/urdf/VLP-16.urdf.xacro\n'}
[0.134887] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/urdf/HDL-32E.urdf.xacro\n'}
[0.136652] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/world\n'}
[0.136835] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/world/example.world\n'}
[0.136907] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/velodyne_description.dsv\n'}
[0.137554] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/ament_index/resource_index/package_run_dependencies/velodyne_description\n'}
[0.137625] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/ament_index/resource_index/parent_prefix_path/velodyne_description\n'}
[0.137681] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/ament_prefix_path.sh\n'}
[0.137736] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/ament_prefix_path.dsv\n'}
[0.137792] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/path.sh\n'}
[0.137911] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/environment/path.dsv\n'}
[0.137966] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/local_setup.bash\n'}
[0.138057] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/local_setup.sh\n'}
[0.138102] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/local_setup.zsh\n'}
[0.138143] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/local_setup.dsv\n'}
[0.138181] (velodyne_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.dsv\n'}
[0.138219] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/ament_index/resource_index/packages/velodyne_description\n'}
[0.138256] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/cmake/velodyne_descriptionConfig.cmake\n'}
[0.138294] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/cmake/velodyne_descriptionConfig-version.cmake\n'}
[0.138333] (velodyne_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.xml\n'}
[0.138403] (tare_planner) StdoutLine: {'line': b'[ 31%] Built target lidar_model\n'}
[0.138468] (velodyne_gazebo_plugins) CommandEnded: {'returncode': 0}
[0.138759] (tare_planner) StdoutLine: {'line': b'[ 36%] Built target tare_visualizer\n'}
[0.139058] (sensor_scan_generation) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.139195] (velodyne_gazebo_plugins) JobProgress: {'identifier': 'velodyne_gazebo_plugins', 'progress': 'install'}
[0.139230] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/lib/sensor_scan_generation/sensorScanGeneration\n'}
[0.139438] (tare_planner) StdoutLine: {'line': b'[ 42%] Built target misc_utils\n'}
[0.139553] (velodyne_gazebo_plugins) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins'], 'cwd': '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.140153] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/launch\n'}
[0.140221] (tare_planner) StdoutLine: {'line': b'[ 47%] Built target navigationBoundary\n'}
[0.140268] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/launch/sensor_scan_generation.launch\n'}
[0.140312] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/ament_index/resource_index/package_run_dependencies/sensor_scan_generation\n'}
[0.140348] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/ament_index/resource_index/parent_prefix_path/sensor_scan_generation\n'}
[0.140380] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/environment/ament_prefix_path.sh\n'}
[0.140411] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/environment/ament_prefix_path.dsv\n'}
[0.140441] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/environment/path.sh\n'}
[0.140470] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/environment/path.dsv\n'}
[0.140500] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.bash\n'}
[0.140530] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.sh\n'}
[0.140560] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.zsh\n'}
[0.140592] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.dsv\n'}
[0.140637] (sensor_scan_generation) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv\n'}
[0.140684] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/ament_index/resource_index/packages/sensor_scan_generation\n'}
[0.140730] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/cmake/sensor_scan_generationConfig.cmake\n'}
[0.140882] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/cmake/sensor_scan_generationConfig-version.cmake\n'}
[0.140935] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.xml\n'}
[0.140985] (velodyne_description) CommandEnded: {'returncode': 0}
[0.141286] (visualization_tools) StdoutLine: {'line': b'[  0%] Built target ament_cmake_python_copy_visualization_tools\n'}
[0.142363] (waypoint_rviz_plugin) StdoutLine: {'line': b'[ 16%] \x1b[34m\x1b[1mAutomatic MOC for target waypoint_rviz_plugin\x1b[0m\n'}
[0.142650] (terrain_analysis_ext) StdoutLine: {'line': b'[100%] Built target terrainAnalysisExt\n'}
[0.143118] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target viewpoint\x1b[0m\n'}
[0.144006] (tare_planner) StdoutLine: {'line': b'[ 52%] Built target rolling_occupancy_grid\n'}
[0.145078] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rolling_grid\x1b[0m\n'}
[0.146597] (waypoint_example) StdoutLine: {'line': b'[100%] Built target waypointExample\n'}
[0.146805] (visualization_tools) StdoutLine: {'line': b'[100%] Built target visualizationTools\n'}
[0.152282] (vehicle_simulator) StdoutLine: {'line': b'[100%] Built target vehicleSimulator\n'}
[0.152701] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target planning_env\x1b[0m\n'}
[0.158259] (tare_planner) StdoutLine: {'line': b'[ 57%] Built target viewpoint\n'}
[0.159090] (waypoint_rviz_plugin) StdoutLine: {'line': b'[ 16%] Built target waypoint_rviz_plugin_autogen\n'}
[0.160605] (velodyne_description) JobEnded: {'identifier': 'velodyne_description', 'rc': 0}
[0.161546] (terrain_analysis) CommandEnded: {'returncode': 0}
[0.161771] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.161881] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib/libgazebo_ros_velodyne_laser.so\n'}
[0.161919] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/include/velodyne_gazebo_plugins\n'}
[0.161951] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/include/velodyne_gazebo_plugins/GazeboRosVelodyneLaser.hpp\n'}
[0.161983] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/environment/library_path.sh\n'}
[0.162014] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/environment/library_path.dsv\n'}
[0.162045] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/ament_index/resource_index/package_run_dependencies/velodyne_gazebo_plugins\n'}
[0.162077] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/ament_index/resource_index/parent_prefix_path/velodyne_gazebo_plugins\n'}
[0.162106] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/environment/ament_prefix_path.sh\n'}
[0.162137] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/environment/ament_prefix_path.dsv\n'}
[0.162168] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/environment/path.sh\n'}
[0.162196] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/environment/path.dsv\n'}
[0.162299] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/local_setup.bash\n'}
[0.162329] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/local_setup.sh\n'}
[0.162358] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/local_setup.zsh\n'}
[0.162387] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/local_setup.dsv\n'}
[0.162415] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.dsv\n'}
[0.162443] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/ament_index/resource_index/packages/velodyne_gazebo_plugins\n'}
[0.162471] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[0.162498] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[0.162526] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.162554] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/cmake/velodyne_gazebo_pluginsConfig.cmake\n'}
[0.162581] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/cmake/velodyne_gazebo_pluginsConfig-version.cmake\n'}
[0.162611] (velodyne_gazebo_plugins) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.xml\n'}
[0.162640] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'install'}
[0.164164] (terrain_analysis) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/terrain_analysis'], 'cwd': '/home/<USER>/zhaoluye/build/terrain_analysis', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/terrain_analysis'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.164906] (loam_interface) CommandEnded: {'returncode': 0}
[0.165394] (loam_interface) JobProgress: {'identifier': 'loam_interface', 'progress': 'install'}
[0.165727] (loam_interface) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/loam_interface'], 'cwd': '/home/<USER>/zhaoluye/build/loam_interface', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/loam_interface'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.167047] (local_planner) CommandEnded: {'returncode': 0}
[0.167746] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'install'}
[0.167997] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/local_planner'], 'cwd': '/home/<USER>/zhaoluye/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/local_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.169873] (tare_planner) StdoutLine: {'line': b'[ 63%] Built target rolling_grid\n'}
[0.170184] (sensor_scan_generation) CommandEnded: {'returncode': 0}
[0.170545] (tare_planner) StdoutLine: {'line': b'[ 68%] Built target planning_env\n'}
[0.170680] (terrain_analysis) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.170832] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/lib/terrain_analysis/terrainAnalysis\n'}
[0.170981] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/launch\n'}
[0.171121] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/launch/terrain_analysis.launch\n'}
[0.171262] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/ament_index/resource_index/package_run_dependencies/terrain_analysis\n'}
[0.171311] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/ament_index/resource_index/parent_prefix_path/terrain_analysis\n'}
[0.171352] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.sh\n'}
[0.171393] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.dsv\n'}
[0.171431] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/environment/path.sh\n'}
[0.171468] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/environment/path.dsv\n'}
[0.171506] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/local_setup.bash\n'}
[0.171543] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/local_setup.sh\n'}
[0.171580] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/local_setup.zsh\n'}
[0.171617] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/local_setup.dsv\n'}
[0.171652] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.dsv\n'}
[0.171687] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/ament_index/resource_index/packages/terrain_analysis\n'}
[0.171723] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig.cmake\n'}
[0.171758] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig-version.cmake\n'}
[0.171815] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.xml\n'}
[0.171862] (waypoint_rviz_plugin) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target waypoint_rviz_plugin\x1b[0m\n'}
[0.173054] (loam_interface) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.173327] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/lib/loam_interface/loamInterface\n'}
[0.174224] (local_planner) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.174493] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/launch\n'}
[0.174628] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/lib/local_planner/localPlanner\n'}
[0.174669] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/lib/local_planner/pathFollower\n'}
[0.174705] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/launch/loam_interface.launch\n'}
[0.174737] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/ament_index/resource_index/package_run_dependencies/loam_interface\n'}
[0.174769] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/ament_index/resource_index/parent_prefix_path/loam_interface\n'}
[0.174806] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/environment/ament_prefix_path.sh\n'}
[0.174998] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/environment/ament_prefix_path.dsv\n'}
[0.175029] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/environment/path.sh\n'}
[0.175059] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/environment/path.dsv\n'}
[0.175089] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/local_setup.bash\n'}
[0.175117] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/local_setup.sh\n'}
[0.175146] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/local_setup.zsh\n'}
[0.175173] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/local_setup.dsv\n'}
[0.175242] (loam_interface) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.dsv\n'}
[0.175275] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/ament_index/resource_index/packages/loam_interface\n'}
[0.175304] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/cmake/loam_interfaceConfig.cmake\n'}
[0.175333] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/cmake/loam_interfaceConfig-version.cmake\n'}
[0.175368] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.xml\n'}
[0.175410] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/launch\n'}
[0.175447] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/launch/local_planner.launch.py\n'}
[0.175478] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/launch/local_planner.launch\n'}
[0.175509] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths\n'}
[0.175542] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/startPaths.ply\n'}
[0.175573] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/correspondences.txt\n'}
[0.175604] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/pathList.ply\n'}
[0.175635] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/paths.ply\n'}
[0.175722] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/path_generator.m\n'}
[0.175756] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/config\n'}
[0.175794] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/ament_index/resource_index/package_run_dependencies/local_planner\n'}
[0.175829] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/ament_index/resource_index/parent_prefix_path/local_planner\n'}
[0.175861] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/environment/ament_prefix_path.sh\n'}
[0.175893] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/environment/ament_prefix_path.dsv\n'}
[0.175925] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/environment/path.sh\n'}
[0.175957] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/environment/path.dsv\n'}
[0.175988] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/local_setup.bash\n'}
[0.176019] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/local_setup.sh\n'}
[0.176050] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/local_setup.zsh\n'}
[0.176081] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/local_setup.dsv\n'}
[0.176114] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.dsv\n'}
[0.176146] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/ament_index/resource_index/packages/local_planner\n'}
[0.176178] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/cmake/local_plannerConfig.cmake\n'}
[0.176210] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/cmake/local_plannerConfig-version.cmake\n'}
[0.176241] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.xml\n'}
[0.176272] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target keypose_graph\x1b[0m\n'}
[0.180581] (sensor_scan_generation) JobEnded: {'identifier': 'sensor_scan_generation', 'rc': 0}
[0.181474] (velodyne_gazebo_plugins) CommandEnded: {'returncode': 0}
[0.185043] (waypoint_rviz_plugin) StdoutLine: {'line': b'[100%] Built target waypoint_rviz_plugin\n'}
[0.189806] (velodyne_gazebo_plugins) JobEnded: {'identifier': 'velodyne_gazebo_plugins', 'rc': 0}
[0.191303] (waypoint_example) CommandEnded: {'returncode': 0}
[0.192553] (waypoint_example) JobProgress: {'identifier': 'waypoint_example', 'progress': 'install'}
[0.192618] (waypoint_example) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/waypoint_example'], 'cwd': '/home/<USER>/zhaoluye/build/waypoint_example', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/waypoint_example'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.193947] (terrain_analysis_ext) CommandEnded: {'returncode': 0}
[0.194149] (terrain_analysis_ext) JobProgress: {'identifier': 'terrain_analysis_ext', 'progress': 'install'}
[0.194170] (terrain_analysis_ext) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/terrain_analysis_ext'], 'cwd': '/home/<USER>/zhaoluye/build/terrain_analysis_ext', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/terrain_analysis_ext'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.194435] (vehicle_simulator) CommandEnded: {'returncode': 0}
[0.194703] (vehicle_simulator) JobProgress: {'identifier': 'vehicle_simulator', 'progress': 'install'}
[0.194718] (vehicle_simulator) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/vehicle_simulator'], 'cwd': '/home/<USER>/zhaoluye/build/vehicle_simulator', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/vehicle_simulator'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.195278] (velodyne_simulator) JobStarted: {'identifier': 'velodyne_simulator'}
[0.200117] (waypoint_example) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.200744] (-) TimerEvent: {}
[0.200853] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/lib/waypoint_example/waypointExample\n'}
[0.201408] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/launch\n'}
[0.201457] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/launch/waypoint_example_garage.launch\n'}
[0.201495] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/data\n'}
[0.201531] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/data/waypoints_garage.ply\n'}
[0.201564] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/data/boundary_garage.ply\n'}
[0.201607] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/ament_index/resource_index/package_run_dependencies/waypoint_example\n'}
[0.201759] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/ament_index/resource_index/parent_prefix_path/waypoint_example\n'}
[0.201801] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/environment/ament_prefix_path.sh\n'}
[0.201835] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/environment/ament_prefix_path.dsv\n'}
[0.201865] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/environment/path.sh\n'}
[0.201897] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/environment/path.dsv\n'}
[0.201928] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/local_setup.bash\n'}
[0.201959] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/local_setup.sh\n'}
[0.201991] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/local_setup.zsh\n'}
[0.202365] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/local_setup.dsv\n'}
[0.202403] (waypoint_example) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.dsv\n'}
[0.202438] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/ament_index/resource_index/packages/waypoint_example\n'}
[0.202469] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/cmake/waypoint_exampleConfig.cmake\n'}
[0.202501] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/cmake/waypoint_exampleConfig-version.cmake\n'}
[0.202534] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.xml\n'}
[0.202566] (terrain_analysis_ext) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.202852] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/lib/terrain_analysis_ext/terrainAnalysisExt\n'}
[0.202904] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/launch\n'}
[0.202950] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/launch/terrain_analysis_ext.launch\n'}
[0.202989] (vehicle_simulator) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.203034] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/lib/vehicle_simulator/vehicleSimulator\n'}
[0.203072] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch\n'}
[0.203109] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_indoor.launch\n'}
[0.203145] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_real_robot_map.launch\n'}
[0.203182] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_campus.launch\n'}
[0.203219] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_real_robot.launch\n'}
[0.203255] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_forest.launch\n'}
[0.203291] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/vehicle_simulator.launch\n'}
[0.203326] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_garage.launch\n'}
[0.203362] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/launch/system_tunnel.launch\n'}
[0.203397] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/log\n'}
[0.203434] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/log/metrics_2025-8-18-15-59-20.txt\n'}
[0.203469] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/log/metrics_2025-8-14-15-41-31.txt\n'}
[0.203504] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/log/trajectory_2025-8-18-15-59-20.txt\n'}
[0.203540] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/log/trajectory_2025-8-14-15-41-31.txt\n'}
[0.203594] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh\n'}
[0.203631] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus\n'}
[0.203667] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/model.sdf\n'}
[0.203703] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/preview\n'}
[0.203739] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/preview/pointcloud.ply\n'}
[0.203774] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/preview/overview.png\n'}
[0.203815] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/model.config\n'}
[0.203851] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes\n'}
[0.203886] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/texture_1001.png.001.png\n'}
[0.203921] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/photo_2020-11-01_11-54-02.jpg\n'}
[0.203956] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/leaf3.png.002.png\n'}
[0.203990] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/texture_1004.png.001.png\n'}
[0.204025] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/bark1.jpg\n'}
[0.204074] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/photo_2020-11-01_11-56-17.jpg\n'}
[0.204113] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/texture_1005.png.001.png\n'}
[0.204149] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/texture_1002.png.001.png\n'}
[0.204185] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/campus.dae\n'}
[0.204221] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/campus/meshes/texture_1003.png.001.png\n'}
[0.204256] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest\n'}
[0.204291] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1\n'}
[0.204325] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/ament_index/resource_index/package_run_dependencies/terrain_analysis_ext\n'}
[0.204376] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/ament_index/resource_index/parent_prefix_path/terrain_analysis_ext\n'}
[0.204414] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/ament_prefix_path.sh\n'}
[0.204458] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/ament_prefix_path.dsv\n'}
[0.204496] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/path.sh\n'}
[0.204532] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/path.dsv\n'}
[0.204568] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.bash\n'}
[0.204603] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.sh\n'}
[0.204638] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.zsh\n'}
[0.204673] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.dsv\n'}
[0.204707] (terrain_analysis_ext) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv\n'}
[0.204742] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/model.sdf\n'}
[0.204810] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials\n'}
[0.204914] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials/scripts\n'}
[0.204955] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials/scripts/house_1.material\n'}
[0.204994] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials/textures\n'}
[0.205031] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials/textures/House_1_Spec.png\n'}
[0.205067] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials/textures/House_1_Normal.png\n'}
[0.205104] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/materials/textures/House_1_Diffuse.png\n'}
[0.205140] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/model-1_4.sdf\n'}
[0.205177] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/model.config\n'}
[0.205213] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/model-1_3.sdf\n'}
[0.205249] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/meshes\n'}
[0.205283] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_1/meshes/house_1.dae\n'}
[0.205318] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall\n'}
[0.205353] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/model.sdf\n'}
[0.205387] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/materials\n'}
[0.205438] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/materials/scripts\n'}
[0.205475] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/materials/scripts/grey_wall.material\n'}
[0.205513] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/materials/textures\n'}
[0.205550] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/materials/textures/grey_wall.png\n'}
[0.205585] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/model-1_4.sdf\n'}
[0.205621] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/model.config\n'}
[0.205656] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grey_wall/model-1_3.sdf\n'}
[0.205692] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain\n'}
[0.205738] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/model.sdf\n'}
[0.205776] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/materials\n'}
[0.205818] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/materials/scripts\n'}
[0.205856] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/materials/scripts/grass.material\n'}
[0.205892] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/materials/textures\n'}
[0.205928] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/materials/textures/grass_dry.png\n'}
[0.205964] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/materials/textures/heightmap.png\n'}
[0.205999] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/vrc_driving_terrain/model.config\n'}
[0.206034] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane\n'}
[0.206136] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/ament_index/resource_index/packages/terrain_analysis_ext\n'}
[0.206182] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/cmake/terrain_analysis_extConfig.cmake\n'}
[0.206220] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/cmake/terrain_analysis_extConfig-version.cmake\n'}
[0.206256] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.xml\n'}
[0.206292] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/model.sdf\n'}
[0.206333] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials\n'}
[0.206369] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials/scripts\n'}
[0.206406] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials/scripts/grass.material\n'}
[0.206445] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials/textures\n'}
[0.206485] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials/textures/grass_dry.png\n'}
[0.206524] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials/textures/flat_normal.png\n'}
[0.206562] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/materials/textures/Grass_Albedo.jpg\n'}
[0.206600] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/grass_plane/model.config\n'}
[0.206636] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/preview\n'}
[0.206672] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/preview/pointcloud.ply\n'}
[0.206709] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/preview/overview.png\n'}
[0.206746] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2\n'}
[0.206786] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/model.sdf\n'}
[0.206837] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/materials\n'}
[0.206875] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/materials/scripts\n'}
[0.206913] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/materials/scripts/house_2.material\n'}
[0.206950] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/materials/textures\n'}
[0.206986] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/materials/textures/House_2_Diffuse.png\n'}
[0.207023] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/model-1_4.sdf\n'}
[0.207059] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/model.config\n'}
[0.207095] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/model-1_3.sdf\n'}
[0.207143] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/meshes\n'}
[0.207265] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/house_2/meshes/house_2.dae\n'}
[0.207305] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree\n'}
[0.207343] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/model.sdf\n'}
[0.207380] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials\n'}
[0.207418] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/scripts\n'}
[0.207455] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/scripts/oak_tree.material\n'}
[0.207491] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/scripts/caster_vp.glsl\n'}
[0.207528] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/scripts/caster_fp.glsl\n'}
[0.207564] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/textures\n'}
[0.207600] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/textures/branch_diffuse.png\n'}
[0.207662] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/materials/textures/bark_diffuse.png\n'}
[0.207704] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/model.config\n'}
[0.207742] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/meshes\n'}
[0.207783] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/oak_tree/meshes/oak_tree.dae\n'}
[0.207826] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree\n'}
[0.207861] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/model.sdf\n'}
[0.207898] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/materials\n'}
[0.207935] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/materials/scripts\n'}
[0.207972] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/materials/scripts/pine_tree.material\n'}
[0.208009] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/materials/textures\n'}
[0.208045] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/materials/textures/branch_2_diffuse.png\n'}
[0.208080] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/materials/textures/bark_diffuse.png\n'}
[0.208116] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/model.config\n'}
[0.208165] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/meshes\n'}
[0.208205] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/forest/pine_tree/meshes/pine_tree.dae\n'}
[0.208243] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor\n'}
[0.208280] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/model.sdf\n'}
[0.208317] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/preview\n'}
[0.208354] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/preview/pointcloud.ply\n'}
[0.208389] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/preview/overview.png\n'}
[0.208427] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/model.config\n'}
[0.208462] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/meshes\n'}
[0.208498] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/meshes/indoor.dae\n'}
[0.208533] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/meshes/imgonline-com-ua-tile-PQCma89EHs3BX1.jpg\n'}
[0.208569] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/meshes/imgonline-com-ua-tile-K49qRoCnvbl.jpg\n'}
[0.208605] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/indoor/meshes/imgonline-com-ua-tile-RySXiqZBriXUB4J.jpg\n'}
[0.208640] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel\n'}
[0.208677] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/model.sdf\n'}
[0.208712] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/preview\n'}
[0.208748] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/preview/pointcloud.ply\n'}
[0.208786] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/preview/overview.png\n'}
[0.208857] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/model.config\n'}
[0.208895] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/meshes\n'}
[0.208933] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/meshes/tunnel.dae\n'}
[0.208970] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/tunnel/meshes/wall_texture.jpg\n'}
[0.209032] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage\n'}
[0.209071] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/model.sdf\n'}
[0.209114] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/preview\n'}
[0.209151] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/preview/pointcloud.ply\n'}
[0.209188] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/preview/overview.png\n'}
[0.209225] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/model.config\n'}
[0.209262] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/meshes\n'}
[0.209297] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/meshes/imgonline-com-ua-tile-pLFAF53NCY1Spr.jpg\n'}
[0.209333] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/meshes/imgonline-com-ua-tile-turvdQ7ZY0r.jpg\n'}
[0.209368] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/meshes/blue-concrete-texture-3.jpg\n'}
[0.209403] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/meshes/imgonline-com-ua-tile-RySXiqZBriXUB4J.jpg\n'}
[0.209439] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/mesh/garage/meshes/garage.dae\n'}
[0.209474] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/rviz\n'}
[0.209509] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/rviz/vehicle_simulator.rviz\n'}
[0.209545] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/urdf\n'}
[0.209579] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/urdf/robot.urdf.xacro\n'}
[0.209614] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/urdf/camera.urdf.xacro\n'}
[0.209648] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/urdf/lidar.urdf.xacro\n'}
[0.209684] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/urdf/robot.sdf\n'}
[0.209724] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world\n'}
[0.209763] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world/indoor.world\n'}
[0.209821] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world/garage.world\n'}
[0.209862] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world/test_world.world\n'}
[0.209898] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world/campus.world\n'}
[0.209933] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world/forest.world\n'}
[0.209968] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/world/tunnel.world\n'}
[0.210018] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/ament_index/resource_index/package_run_dependencies/vehicle_simulator\n'}
[0.210055] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/ament_index/resource_index/parent_prefix_path/vehicle_simulator\n'}
[0.210091] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/environment/ament_prefix_path.sh\n'}
[0.210129] (terrain_analysis) CommandEnded: {'returncode': 0}
[0.210337] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/environment/ament_prefix_path.dsv\n'}
[0.210386] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/environment/path.sh\n'}
[0.210427] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/environment/path.dsv\n'}
[0.210464] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/local_setup.bash\n'}
[0.210501] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/local_setup.sh\n'}
[0.210537] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/local_setup.zsh\n'}
[0.210573] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/local_setup.dsv\n'}
[0.210609] (tare_planner) StdoutLine: {'line': b'[ 73%] Built target keypose_graph\n'}
[0.210671] (vehicle_simulator) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.dsv\n'}
[0.210712] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/ament_index/resource_index/packages/vehicle_simulator\n'}
[0.210758] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/cmake/vehicle_simulatorConfig.cmake\n'}
[0.210805] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/cmake/vehicle_simulatorConfig-version.cmake\n'}
[0.210844] (vehicle_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.xml\n'}
[0.214445] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target grid_world\x1b[0m\n'}
[0.217404] (terrain_analysis) JobEnded: {'identifier': 'terrain_analysis', 'rc': 0}
[0.218162] (local_planner) CommandEnded: {'returncode': 0}
[0.224716] (local_planner) JobEnded: {'identifier': 'local_planner', 'rc': 0}
[0.225398] (loam_interface) CommandEnded: {'returncode': 0}
[0.231506] (loam_interface) JobEnded: {'identifier': 'loam_interface', 'rc': 0}
[0.232046] (waypoint_rviz_plugin) CommandEnded: {'returncode': 0}
[0.233090] (waypoint_rviz_plugin) JobProgress: {'identifier': 'waypoint_rviz_plugin', 'progress': 'install'}
[0.233268] (waypoint_rviz_plugin) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin'], 'cwd': '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.234166] (waypoint_example) CommandEnded: {'returncode': 0}
[0.238296] (tare_planner) StdoutLine: {'line': b'[ 78%] Built target grid_world\n'}
[0.243080] (waypoint_example) JobEnded: {'identifier': 'waypoint_example', 'rc': 0}
[0.244515] (terrain_analysis_ext) CommandEnded: {'returncode': 0}
[0.244843] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.244925] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/plugin_description.xml\n'}
[0.244963] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib/waypoint_rviz_plugin/libwaypoint_rviz_plugin.so\n'}
[0.244995] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/ament_index/resource_index/package_run_dependencies/waypoint_rviz_plugin\n'}
[0.245028] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/ament_index/resource_index/parent_prefix_path/waypoint_rviz_plugin\n'}
[0.245061] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/ament_prefix_path.sh\n'}
[0.245092] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/ament_prefix_path.dsv\n'}
[0.245122] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/path.sh\n'}
[0.245153] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/path.dsv\n'}
[0.245183] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.bash\n'}
[0.245213] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.sh\n'}
[0.245242] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.zsh\n'}
[0.245271] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.dsv\n'}
[0.245299] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv\n'}
[0.245328] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/ament_index/resource_index/packages/waypoint_rviz_plugin\n'}
[0.245376] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/ament_index/resource_index/rviz_common__pluginlib__plugin/waypoint_rviz_plugin\n'}
[0.245406] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.245437] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/waypoint_rviz_pluginConfig.cmake\n'}
[0.245466] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/waypoint_rviz_pluginConfig-version.cmake\n'}
[0.245496] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.xml\n'}
[0.247508] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target viewpoint_manager\x1b[0m\n'}
[0.252434] (terrain_analysis_ext) JobEnded: {'identifier': 'terrain_analysis_ext', 'rc': 0}
[0.253491] (velodyne_simulator) JobProgress: {'identifier': 'velodyne_simulator', 'progress': 'cmake'}
[0.253539] (velodyne_simulator) JobProgress: {'identifier': 'velodyne_simulator', 'progress': 'build'}
[0.254026] (velodyne_simulator) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/zhaoluye/build/velodyne_simulator', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/zhaoluye/build/velodyne_simulator', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/velodyne_simulator'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.255457] (vehicle_simulator) CommandEnded: {'returncode': 0}
[0.263078] (vehicle_simulator) JobEnded: {'identifier': 'vehicle_simulator', 'rc': 0}
[0.263872] (waypoint_rviz_plugin) CommandEnded: {'returncode': 0}
[0.270210] (waypoint_rviz_plugin) JobEnded: {'identifier': 'waypoint_rviz_plugin', 'rc': 0}
[0.273883] (tare_planner) StdoutLine: {'line': b'[ 84%] Built target viewpoint_manager\n'}
[0.283894] (velodyne_simulator) CommandEnded: {'returncode': 0}
[0.284627] (velodyne_simulator) JobProgress: {'identifier': 'velodyne_simulator', 'progress': 'install'}
[0.285033] (velodyne_simulator) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/velodyne_simulator'], 'cwd': '/home/<USER>/zhaoluye/build/velodyne_simulator', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/velodyne_simulator'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.285944] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target local_coverage_planner\x1b[0m\n'}
[0.292494] (velodyne_simulator) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.292765] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/ament_index/resource_index/package_run_dependencies/velodyne_simulator\n'}
[0.292899] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/ament_index/resource_index/parent_prefix_path/velodyne_simulator\n'}
[0.292989] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/environment/ament_prefix_path.sh\n'}
[0.293072] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/environment/ament_prefix_path.dsv\n'}
[0.293151] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/environment/path.sh\n'}
[0.293229] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/environment/path.dsv\n'}
[0.293306] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/local_setup.bash\n'}
[0.293382] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/local_setup.sh\n'}
[0.293462] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/local_setup.zsh\n'}
[0.293615] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/local_setup.dsv\n'}
[0.294461] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.dsv\n'}
[0.294584] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/ament_index/resource_index/packages/velodyne_simulator\n'}
[0.294696] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/cmake/velodyne_simulatorConfig.cmake\n'}
[0.294784] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/cmake/velodyne_simulatorConfig-version.cmake\n'}
[0.294961] (velodyne_simulator) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.xml\n'}
[0.295066] (velodyne_simulator) CommandEnded: {'returncode': 0}
[0.299900] (visualization_tools) StdoutLine: {'line': b'running egg_info\n'}
[0.300926] (-) TimerEvent: {}
[0.301494] (visualization_tools) StdoutLine: {'line': b'writing visualization_tools.egg-info/PKG-INFO\n'}
[0.301709] (visualization_tools) StdoutLine: {'line': b'writing dependency_links to visualization_tools.egg-info/dependency_links.txt\n'}
[0.301998] (visualization_tools) StdoutLine: {'line': b'writing top-level names to visualization_tools.egg-info/top_level.txt\n'}
[0.302972] (visualization_tools) StdoutLine: {'line': b"reading manifest file 'visualization_tools.egg-info/SOURCES.txt'\n"}
[0.303195] (velodyne_simulator) JobEnded: {'identifier': 'velodyne_simulator', 'rc': 0}
[0.303452] (visualization_tools) StdoutLine: {'line': b"writing manifest file 'visualization_tools.egg-info/SOURCES.txt'\n"}
[0.306461] (tare_planner) StdoutLine: {'line': b'[ 89%] Built target local_coverage_planner\n'}
[0.317122] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target sensor_coverage_planner_ground\x1b[0m\n'}
[0.321765] (visualization_tools) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_visualization_tools_egg\n'}
[0.329889] (visualization_tools) CommandEnded: {'returncode': 0}
[0.330414] (visualization_tools) JobProgress: {'identifier': 'visualization_tools', 'progress': 'install'}
[0.330844] (visualization_tools) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/visualization_tools'], 'cwd': '/home/<USER>/zhaoluye/build/visualization_tools', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/visualization_tools'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.339858] (visualization_tools) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.340055] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/lib/visualization_tools/visualizationTools\n'}
[0.340210] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/launch\n'}
[0.340270] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/launch/visualization_tools.launch\n'}
[0.340328] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/pythonpath.sh\n'}
[0.340388] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/pythonpath.dsv\n'}
[0.340450] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info\n'}
[0.340510] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/dependency_links.txt\n'}
[0.340595] (tare_planner) StdoutLine: {'line': b'[ 94%] Built target sensor_coverage_planner_ground\n'}
[0.340677] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/SOURCES.txt\n'}
[0.340775] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/top_level.txt\n'}
[0.340913] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/PKG-INFO\n'}
[0.341101] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools\n'}
[0.341261] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools/__init__.py\n'}
[0.351041] (tare_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target tare_planner_node\x1b[0m\n'}
[0.363625] (visualization_tools) StdoutLine: {'line': b"Listing '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools'...\n"}
[0.366482] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/lib/visualization_tools/realTimePlot.py\n'}
[0.366680] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/ament_index/resource_index/package_run_dependencies/visualization_tools\n'}
[0.366775] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/ament_index/resource_index/parent_prefix_path/visualization_tools\n'}
[0.366913] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.sh\n'}
[0.367000] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.dsv\n'}
[0.367081] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/path.sh\n'}
[0.367162] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/path.dsv\n'}
[0.367242] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.bash\n'}
[0.367342] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.sh\n'}
[0.367424] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.zsh\n'}
[0.367505] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.dsv\n'}
[0.367590] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.dsv\n'}
[0.367718] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/ament_index/resource_index/packages/visualization_tools\n'}
[0.368026] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig.cmake\n'}
[0.368180] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig-version.cmake\n'}
[0.368267] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.xml\n'}
[0.368677] (visualization_tools) CommandEnded: {'returncode': 0}
[0.372224] (tare_planner) StdoutLine: {'line': b'[100%] Built target tare_planner_node\n'}
[0.377578] (visualization_tools) JobEnded: {'identifier': 'visualization_tools', 'rc': 0}
[0.381231] (tare_planner) CommandEnded: {'returncode': 0}
[0.381682] (tare_planner) JobProgress: {'identifier': 'tare_planner', 'progress': 'install'}
[0.382213] (tare_planner) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/zhaoluye/build/tare_planner'], 'cwd': '/home/<USER>/zhaoluye/build/tare_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1722'), ('SYSTEMD_EXEC_PID', '2269'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '61198'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/zhaoluye/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:14996'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/zhaoluye/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2007,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2007'), ('INVOCATION_ID', '101bb2db70d14e3b87ee258e45267b70'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:e209d391-89bd-43ad-a82c-a68aa0020830'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/zhaoluye/build/tare_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble')]), 'shell': False}
[0.389821] (tare_planner) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.389935] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/lib/tare_planner/navigationBoundary\n'}
[0.390009] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/lib/tare_planner/tare_planner_node\n'}
[0.390061] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/\n'}
[0.390117] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore.launch\n'}
[0.390147] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_tunnel.launch\n'}
[0.390196] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_garage.launch\n'}
[0.390264] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_matterport.launch\n'}
[0.390294] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_indoor.launch\n'}
[0.390349] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_forest.launch\n'}
[0.390401] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_campus.launch\n'}
[0.390469] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/\n'}
[0.390496] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//tare_planner_ground.rviz\n'}
[0.390522] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner\n'}
[0.390548] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/campus.yaml\n'}
[0.390577] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/tunnel.yaml\n'}
[0.390606] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/garage.yaml\n'}
[0.390649] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/indoor.yaml\n'}
[0.390678] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/matterport.yaml\n'}
[0.390707] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/forest.yaml\n'}
[0.390736] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner\n'}
[0.390763] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/boundary.ply\n'}
[0.390820] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/package_run_dependencies/tare_planner\n'}
[0.390848] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/parent_prefix_path/tare_planner\n'}
[0.390875] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/ament_prefix_path.sh\n'}
[0.390901] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/ament_prefix_path.dsv\n'}
[0.390927] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/path.sh\n'}
[0.390952] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/path.dsv\n'}
[0.390979] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.bash\n'}
[0.391004] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.sh\n'}
[0.391029] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.zsh\n'}
[0.391055] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.dsv\n'}
[0.391083] (tare_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.dsv\n'}
[0.391108] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/packages/tare_planner\n'}
[0.391133] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/cmake/tare_plannerConfig.cmake\n'}
[0.391159] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/cmake/tare_plannerConfig-version.cmake\n'}
[0.391184] (tare_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.xml\n'}
[0.391606] (tare_planner) CommandEnded: {'returncode': 0}
[0.399164] (tare_planner) JobEnded: {'identifier': 'tare_planner', 'rc': 0}
[0.399696] (-) EventReactorShutdown: {}
