[0.084s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.084s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7c0cd3314e20>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7c0cd33149d0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7c0cd33149d0>>)
[0.256s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.257s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.257s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.257s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.257s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.257s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.257s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/zhaoluye'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ignore', 'ignore_ament_install']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore_ament_install'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_pkg']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_pkg'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_meta']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_meta'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ros']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ros'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['cmake', 'python']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'cmake'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'python'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['python_setup_py']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'python_setup_py'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extensions ['ignore', 'ignore_ament_install']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'ignore'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'ignore_ament_install'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extensions ['colcon_pkg']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'colcon_pkg'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extensions ['colcon_meta']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'colcon_meta'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extensions ['ros']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'ros'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extensions ['cmake', 'python']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'cmake'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'python'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extensions ['python_setup_py']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/config) by extension 'python_setup_py'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extensions ['ignore', 'ignore_ament_install']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'ignore'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'ignore_ament_install'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extensions ['colcon_pkg']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'colcon_pkg'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extensions ['colcon_meta']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'colcon_meta'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extensions ['ros']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'ros'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extensions ['cmake', 'python']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'cmake'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'python'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extensions ['python_setup_py']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/launch) by extension 'python_setup_py'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extensions ['ignore', 'ignore_ament_install']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'ignore'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'ignore_ament_install'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extensions ['colcon_pkg']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'colcon_pkg'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extensions ['colcon_meta']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'colcon_meta'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extensions ['ros']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'ros'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extensions ['cmake', 'python']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'cmake'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'python'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extensions ['python_setup_py']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/maps) by extension 'python_setup_py'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'ignore'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'ignore_ament_install'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extensions ['colcon_pkg']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'colcon_pkg'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extensions ['colcon_meta']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'colcon_meta'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extensions ['ros']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'ros'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extensions ['cmake', 'python']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'cmake'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'python'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extensions ['python_setup_py']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation/scripts) by extension 'python_setup_py'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ignore', 'ignore_ament_install']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore_ament_install'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_pkg']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_pkg'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_meta']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_meta'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ros']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ros'
[0.269s] DEBUG:colcon.colcon_core.package_identification:Package 'src/loam_interface' with type 'ros.ament_cmake' and name 'loam_interface'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ignore', 'ignore_ament_install']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore_ament_install'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_pkg']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_pkg'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_meta']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_meta'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ros']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ros'
[0.270s] DEBUG:colcon.colcon_core.package_identification:Package 'src/local_planner' with type 'ros.ament_cmake' and name 'local_planner'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ignore', 'ignore_ament_install']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore_ament_install'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_pkg']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_pkg'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_meta']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_meta'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ros']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ros'
[0.271s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sensor_scan_generation' with type 'ros.ament_cmake' and name 'sensor_scan_generation'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extensions ['ignore', 'ignore_ament_install']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extension 'ignore'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extension 'ignore_ament_install'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extensions ['colcon_pkg']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extension 'colcon_pkg'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extensions ['colcon_meta']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extension 'colcon_meta'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extensions ['ros']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(src/tare_planner) by extension 'ros'
[0.272s] DEBUG:colcon.colcon_core.package_identification:Package 'src/tare_planner' with type 'ros.ament_cmake' and name 'tare_planner'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ignore', 'ignore_ament_install']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore_ament_install'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_pkg']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_pkg'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_meta']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_meta'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ros']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ros'
[0.273s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis' with type 'ros.ament_cmake' and name 'terrain_analysis'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ignore', 'ignore_ament_install']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore_ament_install'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_pkg']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_pkg'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_meta']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_meta'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ros']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ros'
[0.274s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis_ext' with type 'ros.ament_cmake' and name 'terrain_analysis_ext'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'ignore'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'ignore_ament_install'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extensions ['colcon_pkg']
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'colcon_pkg'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extensions ['colcon_meta']
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'colcon_meta'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extensions ['ros']
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'ros'
[0.275s] DEBUG:colcon.colcon_core.package_identification:Package 'src/vehicle_simulator' with type 'ros.ament_cmake' and name 'vehicle_simulator'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'ignore'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'ignore_ament_install'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['colcon_pkg']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'colcon_pkg'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['colcon_meta']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'colcon_meta'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['ros']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'ros'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['cmake', 'python']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'cmake'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'python'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['python_setup_py']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'python_setup_py'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extensions ['ignore', 'ignore_ament_install']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extension 'ignore'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extension 'ignore_ament_install'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extensions ['colcon_pkg']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extension 'colcon_pkg'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extensions ['colcon_meta']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extension 'colcon_meta'
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extensions ['ros']
[0.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_description) by extension 'ros'
[0.277s] DEBUG:colcon.colcon_core.package_identification:Package 'src/velodyne_simulator/velodyne_description' with type 'ros.ament_cmake' and name 'velodyne_description'
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extension 'ignore'
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extension 'ignore_ament_install'
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extensions ['colcon_pkg']
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extension 'colcon_pkg'
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extensions ['colcon_meta']
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extension 'colcon_meta'
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extensions ['ros']
[0.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_gazebo_plugins) by extension 'ros'
[0.278s] DEBUG:colcon.colcon_core.package_identification:Package 'src/velodyne_simulator/velodyne_gazebo_plugins' with type 'ros.ament_cmake' and name 'velodyne_gazebo_plugins'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extension 'ignore'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extension 'ignore_ament_install'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extensions ['colcon_pkg']
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extension 'colcon_pkg'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extensions ['colcon_meta']
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extension 'colcon_meta'
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extensions ['ros']
[0.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator/velodyne_simulator) by extension 'ros'
[0.279s] DEBUG:colcon.colcon_core.package_identification:Package 'src/velodyne_simulator/velodyne_simulator' with type 'ros.ament_cmake' and name 'velodyne_simulator'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ignore', 'ignore_ament_install']
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore_ament_install'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_pkg']
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_pkg'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_meta']
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_meta'
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ros']
[0.279s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ros'
[0.280s] DEBUG:colcon.colcon_core.package_identification:Package 'src/visualization_tools' with type 'ros.ament_cmake' and name 'visualization_tools'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ignore', 'ignore_ament_install']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore_ament_install'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_pkg']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_pkg'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_meta']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_meta'
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ros']
[0.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ros'
[0.280s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_example' with type 'ros.ament_cmake' and name 'waypoint_example'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ignore', 'ignore_ament_install']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore_ament_install'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_pkg']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_pkg'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_meta']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_meta'
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ros']
[0.281s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ros'
[0.282s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_rviz_plugin' with type 'ros.ament_cmake' and name 'waypoint_rviz_plugin'
[0.282s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.282s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.282s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.282s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.282s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.303s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.303s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.305s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 13 installed packages in /home/<USER>/zhaoluye/install
[0.306s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 473 installed packages in /opt/ros/humble
[0.308s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.340s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_target' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_clean_cache' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_clean_first' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_force_configure' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'ament_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'catkin_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.340s] DEBUG:colcon.colcon_core.verb:Building package 'loam_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/loam_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/loam_interface', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/loam_interface', 'symlink_install': False, 'test_result_base': None}
[0.341s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.341s] DEBUG:colcon.colcon_core.verb:Building package 'local_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/local_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/local_planner', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/local_planner', 'symlink_install': False, 'test_result_base': None}
[0.341s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_target' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_clean_cache' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_clean_first' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_force_configure' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'ament_cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'catkin_cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.341s] DEBUG:colcon.colcon_core.verb:Building package 'sensor_scan_generation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/sensor_scan_generation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/sensor_scan_generation', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/sensor_scan_generation', 'symlink_install': False, 'test_result_base': None}
[0.341s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_target' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.341s] DEBUG:colcon.colcon_core.verb:Building package 'tare_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/tare_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/tare_planner', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/tare_planner', 'symlink_install': False, 'test_result_base': None}
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_target' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_clean_cache' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_clean_first' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_force_configure' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'ament_cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'catkin_cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.342s] DEBUG:colcon.colcon_core.verb:Building package 'terrain_analysis' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/terrain_analysis', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/terrain_analysis', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/terrain_analysis', 'symlink_install': False, 'test_result_base': None}
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_target' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_clean_cache' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_clean_first' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_force_configure' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'ament_cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'catkin_cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.342s] DEBUG:colcon.colcon_core.verb:Building package 'terrain_analysis_ext' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/terrain_analysis_ext', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/terrain_analysis_ext', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/terrain_analysis_ext', 'symlink_install': False, 'test_result_base': None}
[0.342s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'cmake_target' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'cmake_clean_cache' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'cmake_clean_first' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'cmake_force_configure' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'ament_cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'catkin_cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'vehicle_simulator' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.342s] DEBUG:colcon.colcon_core.verb:Building package 'vehicle_simulator' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/vehicle_simulator', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/vehicle_simulator', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/vehicle_simulator', 'symlink_install': False, 'test_result_base': None}
[0.342s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'cmake_args' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'cmake_target' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'cmake_clean_first' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'cmake_force_configure' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'ament_cmake_args' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.343s] DEBUG:colcon.colcon_core.verb:Building package 'velodyne_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/velodyne_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/velodyne_description', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_description', 'symlink_install': False, 'test_result_base': None}
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'cmake_args' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'cmake_target' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'velodyne_gazebo_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.343s] DEBUG:colcon.colcon_core.verb:Building package 'velodyne_gazebo_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_gazebo_plugins', 'symlink_install': False, 'test_result_base': None}
[0.343s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_args' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_target' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_clean_cache' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_clean_first' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_force_configure' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'ament_cmake_args' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'catkin_cmake_args' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.343s] DEBUG:colcon.colcon_core.verb:Building package 'visualization_tools' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/visualization_tools', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/visualization_tools', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/visualization_tools', 'symlink_install': False, 'test_result_base': None}
[0.343s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_args' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_target' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_clean_cache' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_clean_first' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_force_configure' from command line to 'False'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'ament_cmake_args' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'catkin_cmake_args' from command line to 'None'
[0.343s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.344s] DEBUG:colcon.colcon_core.verb:Building package 'waypoint_example' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/waypoint_example', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/waypoint_example', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/waypoint_example', 'symlink_install': False, 'test_result_base': None}
[0.344s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_args' from command line to 'None'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_target' from command line to 'None'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_clean_cache' from command line to 'False'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_clean_first' from command line to 'False'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_force_configure' from command line to 'False'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'ament_cmake_args' from command line to 'None'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'catkin_cmake_args' from command line to 'None'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.344s] DEBUG:colcon.colcon_core.verb:Building package 'waypoint_rviz_plugin' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/waypoint_rviz_plugin', 'symlink_install': False, 'test_result_base': None}
[0.344s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'cmake_args' from command line to 'None'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'cmake_target' from command line to 'None'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'cmake_clean_cache' from command line to 'False'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'cmake_clean_first' from command line to 'False'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'cmake_force_configure' from command line to 'False'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'ament_cmake_args' from command line to 'None'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'catkin_cmake_args' from command line to 'None'
[0.344s] Level 5:colcon.colcon_core.verb:set package 'velodyne_simulator' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.344s] DEBUG:colcon.colcon_core.verb:Building package 'velodyne_simulator' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/build/velodyne_simulator', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/install/velodyne_simulator', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_simulator', 'symlink_install': False, 'test_result_base': None}
[0.344s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.345s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.345s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_description' with build type 'ament_cmake'
[0.345s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_description'
[0.348s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.348s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.348s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.351s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_gazebo_plugins' with build type 'ament_cmake'
[0.351s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_gazebo_plugins'
[0.351s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.351s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.354s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/loam_interface' with build type 'ament_cmake'
[0.354s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/loam_interface'
[0.354s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.354s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.356s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/local_planner' with build type 'ament_cmake'
[0.356s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/local_planner'
[0.357s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.357s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.359s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/sensor_scan_generation' with build type 'ament_cmake'
[0.359s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/sensor_scan_generation'
[0.359s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.359s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.362s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/tare_planner' with build type 'ament_cmake'
[0.362s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/tare_planner'
[0.362s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.362s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.364s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/terrain_analysis' with build type 'ament_cmake'
[0.365s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/terrain_analysis'
[0.365s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.365s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.367s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/terrain_analysis_ext' with build type 'ament_cmake'
[0.367s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/terrain_analysis_ext'
[0.367s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.368s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.370s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/vehicle_simulator' with build type 'ament_cmake'
[0.370s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/vehicle_simulator'
[0.370s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.370s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.373s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/visualization_tools' with build type 'ament_cmake'
[0.373s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/visualization_tools'
[0.373s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.373s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.375s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/waypoint_example' with build type 'ament_cmake'
[0.375s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/waypoint_example'
[0.375s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.375s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.378s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/waypoint_rviz_plugin' with build type 'ament_cmake'
[0.378s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/waypoint_rviz_plugin'
[0.378s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.378s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.385s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/velodyne_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_description -- -j16 -l16
[0.388s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_gazebo_plugins -- -j16 -l16
[0.389s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/loam_interface': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/loam_interface -- -j16 -l16
[0.393s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/local_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/local_planner -- -j16 -l16
[0.396s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/sensor_scan_generation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/sensor_scan_generation -- -j16 -l16
[0.398s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/tare_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/tare_planner -- -j16 -l16
[0.402s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/terrain_analysis': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/terrain_analysis -- -j16 -l16
[0.405s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/terrain_analysis_ext': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/terrain_analysis_ext -- -j16 -l16
[0.409s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/vehicle_simulator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/vehicle_simulator -- -j16 -l16
[0.412s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/visualization_tools': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/visualization_tools -- -j16 -l16
[0.417s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/waypoint_example': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/waypoint_example -- -j16 -l16
[0.424s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/waypoint_rviz_plugin -- -j16 -l16
[0.424s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/velodyne_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_description -- -j16 -l16
[0.434s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/velodyne_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_description
[0.471s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/sensor_scan_generation -- -j16 -l16
[0.476s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/sensor_scan_generation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/sensor_scan_generation
[0.482s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(velodyne_description)
[0.484s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_gazebo_plugins -- -j16 -l16
[0.485s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_gazebo_plugins
[0.486s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/velodyne_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_description
[0.489s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description' for CMake module files
[0.490s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description' for CMake config files
[0.491s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_description', 'cmake_prefix_path')
[0.491s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/hook/cmake_prefix_path.ps1'
[0.492s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/hook/cmake_prefix_path.dsv'
[0.492s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/hook/cmake_prefix_path.sh'
[0.493s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/bin'
[0.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/lib/pkgconfig/velodyne_description.pc'
[0.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/lib/python3.10/site-packages'
[0.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/bin'
[0.495s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.ps1'
[0.495s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.dsv'
[0.496s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.sh'
[0.497s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.bash'
[0.498s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.zsh'
[0.499s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/velodyne_description/share/colcon-core/packages/velodyne_description)
[0.500s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(velodyne_description)
[0.500s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description' for CMake module files
[0.501s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description' for CMake config files
[0.501s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_description', 'cmake_prefix_path')
[0.501s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/hook/cmake_prefix_path.ps1'
[0.502s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/hook/cmake_prefix_path.dsv'
[0.502s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/hook/cmake_prefix_path.sh'
[0.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/bin'
[0.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/lib/pkgconfig/velodyne_description.pc'
[0.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/lib/python3.10/site-packages'
[0.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_description/bin'
[0.504s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.ps1'
[0.504s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.dsv'
[0.505s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.sh'
[0.505s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.bash'
[0.505s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_description/share/velodyne_description/package.zsh'
[0.505s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/velodyne_description/share/colcon-core/packages/velodyne_description)
[0.507s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/terrain_analysis -- -j16 -l16
[0.510s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/terrain_analysis': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/terrain_analysis
[0.510s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/loam_interface' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/loam_interface -- -j16 -l16
[0.511s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/loam_interface': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/loam_interface
[0.512s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/local_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/local_planner -- -j16 -l16
[0.514s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sensor_scan_generation)
[0.514s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/local_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/local_planner
[0.514s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation' for CMake module files
[0.515s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/sensor_scan_generation
[0.516s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation' for CMake config files
[0.517s] Level 1:colcon.colcon_core.shell:create_environment_hook('sensor_scan_generation', 'cmake_prefix_path')
[0.517s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.ps1'
[0.517s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.dsv'
[0.518s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.sh'
[0.518s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/lib'
[0.518s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/bin'
[0.518s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/lib/pkgconfig/sensor_scan_generation.pc'
[0.518s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/lib/python3.10/site-packages'
[0.519s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/bin'
[0.519s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.ps1'
[0.519s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv'
[0.521s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.sh'
[0.522s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.bash'
[0.522s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.zsh'
[0.522s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/sensor_scan_generation/share/colcon-core/packages/sensor_scan_generation)
[0.522s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sensor_scan_generation)
[0.522s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation' for CMake module files
[0.523s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation' for CMake config files
[0.523s] Level 1:colcon.colcon_core.shell:create_environment_hook('sensor_scan_generation', 'cmake_prefix_path')
[0.523s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.ps1'
[0.523s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.dsv'
[0.523s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.sh'
[0.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/lib'
[0.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/bin'
[0.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/lib/pkgconfig/sensor_scan_generation.pc'
[0.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/lib/python3.10/site-packages'
[0.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/sensor_scan_generation/bin'
[0.524s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.ps1'
[0.524s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv'
[0.525s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.sh'
[0.525s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.bash'
[0.525s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.zsh'
[0.525s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/sensor_scan_generation/share/colcon-core/packages/sensor_scan_generation)
[0.525s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(velodyne_gazebo_plugins)
[0.526s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins' for CMake module files
[0.526s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins' for CMake config files
[0.527s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/velodyne_gazebo_plugins' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_gazebo_plugins
[0.527s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_gazebo_plugins', 'cmake_prefix_path')
[0.527s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/cmake_prefix_path.ps1'
[0.527s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/cmake_prefix_path.dsv'
[0.527s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/cmake_prefix_path.sh'
[0.528s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib'
[0.528s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_gazebo_plugins', 'ld_library_path_lib')
[0.528s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/ld_library_path_lib.ps1'
[0.528s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/ld_library_path_lib.dsv'
[0.528s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/ld_library_path_lib.sh'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/bin'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib/pkgconfig/velodyne_gazebo_plugins.pc'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib/python3.10/site-packages'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/bin'
[0.529s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.ps1'
[0.529s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.dsv'
[0.530s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.sh'
[0.530s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.bash'
[0.530s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.zsh'
[0.530s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/colcon-core/packages/velodyne_gazebo_plugins)
[0.531s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(velodyne_gazebo_plugins)
[0.531s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins' for CMake module files
[0.531s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins' for CMake config files
[0.531s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_gazebo_plugins', 'cmake_prefix_path')
[0.531s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/cmake_prefix_path.ps1'
[0.531s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/cmake_prefix_path.dsv'
[0.532s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/cmake_prefix_path.sh'
[0.532s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib'
[0.532s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_gazebo_plugins', 'ld_library_path_lib')
[0.532s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/ld_library_path_lib.ps1'
[0.532s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/ld_library_path_lib.dsv'
[0.533s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/hook/ld_library_path_lib.sh'
[0.533s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/bin'
[0.533s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib/pkgconfig/velodyne_gazebo_plugins.pc'
[0.533s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/lib/python3.10/site-packages'
[0.533s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/bin'
[0.533s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.ps1'
[0.534s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.dsv'
[0.534s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.sh'
[0.534s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.bash'
[0.534s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/velodyne_gazebo_plugins/package.zsh'
[0.534s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins/share/colcon-core/packages/velodyne_gazebo_plugins)
[0.537s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/waypoint_example' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/waypoint_example -- -j16 -l16
[0.539s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/waypoint_example': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/waypoint_example
[0.539s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/terrain_analysis_ext' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/terrain_analysis_ext -- -j16 -l16
[0.539s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/terrain_analysis_ext': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/terrain_analysis_ext
[0.540s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/vehicle_simulator' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/vehicle_simulator -- -j16 -l16
[0.540s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_simulator' with build type 'ament_cmake'
[0.540s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/vehicle_simulator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/vehicle_simulator
[0.540s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/velodyne_simulator/velodyne_simulator'
[0.541s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.541s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.546s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis)
[0.547s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis' for CMake module files
[0.552s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis' for CMake config files
[0.555s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/terrain_analysis
[0.556s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis', 'cmake_prefix_path')
[0.556s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.ps1'
[0.556s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.dsv'
[0.557s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.sh'
[0.557s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/lib'
[0.557s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/bin'
[0.557s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/lib/pkgconfig/terrain_analysis.pc'
[0.557s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/lib/python3.10/site-packages'
[0.557s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/bin'
[0.558s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.ps1'
[0.558s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.dsv'
[0.558s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.sh'
[0.558s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.bash'
[0.559s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.zsh'
[0.559s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/terrain_analysis/share/colcon-core/packages/terrain_analysis)
[0.559s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis)
[0.559s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis' for CMake module files
[0.560s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis' for CMake config files
[0.560s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis', 'cmake_prefix_path')
[0.560s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.ps1'
[0.560s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.dsv'
[0.560s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.sh'
[0.560s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/lib'
[0.561s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/bin'
[0.561s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/lib/pkgconfig/terrain_analysis.pc'
[0.561s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/lib/python3.10/site-packages'
[0.561s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis/bin'
[0.561s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.ps1'
[0.561s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.dsv'
[0.562s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.sh'
[0.562s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.bash'
[0.562s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis/share/terrain_analysis/package.zsh'
[0.562s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/terrain_analysis/share/colcon-core/packages/terrain_analysis)
[0.562s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[0.563s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner' for CMake module files
[0.563s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/local_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/local_planner
[0.563s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner' for CMake config files
[0.564s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[0.564s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[0.564s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[0.564s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[0.565s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/lib'
[0.565s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/bin'
[0.565s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/lib/pkgconfig/local_planner.pc'
[0.565s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/lib/python3.10/site-packages'
[0.565s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/bin'
[0.565s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.ps1'
[0.566s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.dsv'
[0.566s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.sh'
[0.566s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.bash'
[0.566s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.zsh'
[0.566s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/local_planner/share/colcon-core/packages/local_planner)
[0.567s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[0.567s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner' for CMake module files
[0.567s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner' for CMake config files
[0.567s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[0.567s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[0.567s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[0.568s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[0.568s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/lib'
[0.568s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/bin'
[0.568s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/lib/pkgconfig/local_planner.pc'
[0.568s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/lib/python3.10/site-packages'
[0.568s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/local_planner/bin'
[0.568s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.ps1'
[0.569s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.dsv'
[0.569s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.sh'
[0.569s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.bash'
[0.569s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.zsh'
[0.569s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/local_planner/share/colcon-core/packages/local_planner)
[0.570s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(loam_interface)
[0.570s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface' for CMake module files
[0.571s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/loam_interface' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/loam_interface
[0.571s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface' for CMake config files
[0.571s] Level 1:colcon.colcon_core.shell:create_environment_hook('loam_interface', 'cmake_prefix_path')
[0.571s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.ps1'
[0.571s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.dsv'
[0.571s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.sh'
[0.572s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/lib'
[0.572s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/bin'
[0.572s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/lib/pkgconfig/loam_interface.pc'
[0.572s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/lib/python3.10/site-packages'
[0.572s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/bin'
[0.572s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.ps1'
[0.572s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.dsv'
[0.573s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.sh'
[0.573s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.bash'
[0.573s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.zsh'
[0.573s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/loam_interface/share/colcon-core/packages/loam_interface)
[0.573s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(loam_interface)
[0.574s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface' for CMake module files
[0.574s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface' for CMake config files
[0.574s] Level 1:colcon.colcon_core.shell:create_environment_hook('loam_interface', 'cmake_prefix_path')
[0.574s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.ps1'
[0.574s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.dsv'
[0.574s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.sh'
[0.575s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/lib'
[0.575s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/bin'
[0.575s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/lib/pkgconfig/loam_interface.pc'
[0.575s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/lib/python3.10/site-packages'
[0.575s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/loam_interface/bin'
[0.575s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.ps1'
[0.575s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.dsv'
[0.576s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.sh'
[0.576s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.bash'
[0.576s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/loam_interface/share/loam_interface/package.zsh'
[0.576s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/loam_interface/share/colcon-core/packages/loam_interface)
[0.578s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/waypoint_rviz_plugin -- -j16 -l16
[0.579s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_example)
[0.579s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/waypoint_rviz_plugin
[0.579s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example' for CMake module files
[0.580s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/waypoint_example' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/waypoint_example
[0.580s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example' for CMake config files
[0.580s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_example', 'cmake_prefix_path')
[0.580s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.ps1'
[0.580s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.dsv'
[0.581s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.sh'
[0.581s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/lib'
[0.581s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/bin'
[0.581s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/lib/pkgconfig/waypoint_example.pc'
[0.581s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/lib/python3.10/site-packages'
[0.581s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/bin'
[0.581s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.ps1'
[0.582s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.dsv'
[0.582s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.sh'
[0.582s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.bash'
[0.582s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.zsh'
[0.583s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/waypoint_example/share/colcon-core/packages/waypoint_example)
[0.583s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_example)
[0.583s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example' for CMake module files
[0.584s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example' for CMake config files
[0.584s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_example', 'cmake_prefix_path')
[0.584s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.ps1'
[0.585s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.dsv'
[0.585s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.sh'
[0.586s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/lib'
[0.586s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/bin'
[0.586s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/lib/pkgconfig/waypoint_example.pc'
[0.586s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/lib/python3.10/site-packages'
[0.586s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_example/bin'
[0.587s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.ps1'
[0.587s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.dsv'
[0.587s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.sh'
[0.587s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.bash'
[0.588s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_example/share/waypoint_example/package.zsh'
[0.588s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/waypoint_example/share/colcon-core/packages/waypoint_example)
[0.589s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis_ext)
[0.589s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext' for CMake module files
[0.590s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext' for CMake config files
[0.590s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/terrain_analysis_ext' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/terrain_analysis_ext
[0.590s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis_ext', 'cmake_prefix_path')
[0.591s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.ps1'
[0.591s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.dsv'
[0.591s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.sh'
[0.592s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/lib'
[0.592s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/bin'
[0.592s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/lib/pkgconfig/terrain_analysis_ext.pc'
[0.592s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/lib/python3.10/site-packages'
[0.592s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/bin'
[0.592s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.ps1'
[0.593s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv'
[0.593s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.sh'
[0.593s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.bash'
[0.594s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.zsh'
[0.594s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/colcon-core/packages/terrain_analysis_ext)
[0.594s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis_ext)
[0.594s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext' for CMake module files
[0.594s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext' for CMake config files
[0.595s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis_ext', 'cmake_prefix_path')
[0.595s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.ps1'
[0.595s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.dsv'
[0.595s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.sh'
[0.595s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/lib'
[0.595s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/bin'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/lib/pkgconfig/terrain_analysis_ext.pc'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/lib/python3.10/site-packages'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/terrain_analysis_ext/bin'
[0.596s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.ps1'
[0.596s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv'
[0.597s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.sh'
[0.597s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.bash'
[0.597s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/terrain_analysis_ext/package.zsh'
[0.597s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/terrain_analysis_ext/share/colcon-core/packages/terrain_analysis_ext)
[0.599s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(vehicle_simulator)
[0.600s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator' for CMake module files
[0.600s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/velodyne_simulator': CMAKE_PREFIX_PATH=/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_simulator -- -j16 -l16
[0.601s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/vehicle_simulator' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/vehicle_simulator
[0.601s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator' for CMake config files
[0.601s] Level 1:colcon.colcon_core.shell:create_environment_hook('vehicle_simulator', 'cmake_prefix_path')
[0.601s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/hook/cmake_prefix_path.ps1'
[0.601s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/hook/cmake_prefix_path.dsv'
[0.602s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/hook/cmake_prefix_path.sh'
[0.602s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/lib'
[0.602s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/bin'
[0.602s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/lib/pkgconfig/vehicle_simulator.pc'
[0.602s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/lib/python3.10/site-packages'
[0.602s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/bin'
[0.603s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.ps1'
[0.603s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.dsv'
[0.603s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.sh'
[0.603s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.bash'
[0.603s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.zsh'
[0.604s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/vehicle_simulator/share/colcon-core/packages/vehicle_simulator)
[0.604s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(vehicle_simulator)
[0.604s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator' for CMake module files
[0.604s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator' for CMake config files
[0.605s] Level 1:colcon.colcon_core.shell:create_environment_hook('vehicle_simulator', 'cmake_prefix_path')
[0.605s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/hook/cmake_prefix_path.ps1'
[0.605s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/hook/cmake_prefix_path.dsv'
[0.605s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/hook/cmake_prefix_path.sh'
[0.606s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/lib'
[0.606s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/bin'
[0.606s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/lib/pkgconfig/vehicle_simulator.pc'
[0.606s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/lib/python3.10/site-packages'
[0.606s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/vehicle_simulator/bin'
[0.606s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.ps1'
[0.606s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.dsv'
[0.607s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.sh'
[0.607s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.bash'
[0.607s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/vehicle_simulator/share/vehicle_simulator/package.zsh'
[0.607s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/vehicle_simulator/share/colcon-core/packages/vehicle_simulator)
[0.608s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_rviz_plugin)
[0.608s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin' for CMake module files
[0.609s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin' for CMake config files
[0.609s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/waypoint_rviz_plugin' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/waypoint_rviz_plugin
[0.609s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_rviz_plugin', 'cmake_prefix_path')
[0.609s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.ps1'
[0.610s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.dsv'
[0.610s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.sh'
[0.610s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib'
[0.610s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/bin'
[0.610s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib/pkgconfig/waypoint_rviz_plugin.pc'
[0.610s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib/python3.10/site-packages'
[0.611s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/bin'
[0.611s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.ps1'
[0.611s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv'
[0.611s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.sh'
[0.611s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.bash'
[0.612s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.zsh'
[0.612s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/colcon-core/packages/waypoint_rviz_plugin)
[0.612s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_rviz_plugin)
[0.612s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin' for CMake module files
[0.612s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin' for CMake config files
[0.612s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_rviz_plugin', 'cmake_prefix_path')
[0.613s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.ps1'
[0.613s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.dsv'
[0.613s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.sh'
[0.613s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib'
[0.613s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/bin'
[0.613s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib/pkgconfig/waypoint_rviz_plugin.pc'
[0.613s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/lib/python3.10/site-packages'
[0.614s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/bin'
[0.614s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.ps1'
[0.614s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv'
[0.614s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.sh'
[0.615s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.bash'
[0.615s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.zsh'
[0.615s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/waypoint_rviz_plugin/share/colcon-core/packages/waypoint_rviz_plugin)
[0.629s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/velodyne_simulator' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_simulator -- -j16 -l16
[0.631s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/velodyne_simulator': CMAKE_PREFIX_PATH=/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_simulator
[0.639s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(velodyne_simulator)
[0.639s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator' for CMake module files
[0.640s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/velodyne_simulator' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_simulator
[0.640s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator' for CMake config files
[0.641s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_simulator', 'cmake_prefix_path')
[0.641s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/hook/cmake_prefix_path.ps1'
[0.641s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/hook/cmake_prefix_path.dsv'
[0.641s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/hook/cmake_prefix_path.sh'
[0.642s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/bin'
[0.642s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/lib/pkgconfig/velodyne_simulator.pc'
[0.642s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/lib/python3.10/site-packages'
[0.642s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/bin'
[0.642s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.ps1'
[0.643s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.dsv'
[0.643s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.sh'
[0.643s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.bash'
[0.643s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.zsh'
[0.643s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/velodyne_simulator/share/colcon-core/packages/velodyne_simulator)
[0.644s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(velodyne_simulator)
[0.644s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator' for CMake module files
[0.644s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator' for CMake config files
[0.644s] Level 1:colcon.colcon_core.shell:create_environment_hook('velodyne_simulator', 'cmake_prefix_path')
[0.644s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/hook/cmake_prefix_path.ps1'
[0.645s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/hook/cmake_prefix_path.dsv'
[0.645s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/hook/cmake_prefix_path.sh'
[0.645s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/bin'
[0.645s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/lib/pkgconfig/velodyne_simulator.pc'
[0.645s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/lib/python3.10/site-packages'
[0.646s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/velodyne_simulator/bin'
[0.646s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.ps1'
[0.646s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.dsv'
[0.647s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.sh'
[0.647s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.bash'
[0.647s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.zsh'
[0.648s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/velodyne_simulator/share/colcon-core/packages/velodyne_simulator)
[0.675s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/visualization_tools -- -j16 -l16
[0.676s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/visualization_tools': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/visualization_tools
[0.713s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(visualization_tools)
[0.714s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools' for CMake module files
[0.714s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/visualization_tools
[0.715s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools' for CMake config files
[0.715s] Level 1:colcon.colcon_core.shell:create_environment_hook('visualization_tools', 'cmake_prefix_path')
[0.715s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.ps1'
[0.716s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.dsv'
[0.716s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.sh'
[0.717s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/lib'
[0.717s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/bin'
[0.717s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/lib/pkgconfig/visualization_tools.pc'
[0.718s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/lib/python3.10/site-packages'
[0.718s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/bin'
[0.718s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.ps1'
[0.718s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.dsv'
[0.719s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.sh'
[0.719s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.bash'
[0.719s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.zsh'
[0.719s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/visualization_tools/share/colcon-core/packages/visualization_tools)
[0.719s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(visualization_tools)
[0.720s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools' for CMake module files
[0.720s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools' for CMake config files
[0.720s] Level 1:colcon.colcon_core.shell:create_environment_hook('visualization_tools', 'cmake_prefix_path')
[0.720s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.ps1'
[0.720s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.dsv'
[0.720s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.sh'
[0.721s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/lib'
[0.721s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/bin'
[0.721s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/lib/pkgconfig/visualization_tools.pc'
[0.721s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/lib/python3.10/site-packages'
[0.721s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/visualization_tools/bin'
[0.721s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.ps1'
[0.721s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.dsv'
[0.722s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.sh'
[0.722s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.bash'
[0.722s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.zsh'
[0.722s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/visualization_tools/share/colcon-core/packages/visualization_tools)
[0.726s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/tare_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/tare_planner -- -j16 -l16
[0.728s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/build/tare_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/tare_planner
[0.736s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(tare_planner)
[0.737s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/zhaoluye/build/tare_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/tare_planner
[0.737s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner' for CMake module files
[0.737s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner' for CMake config files
[0.737s] Level 1:colcon.colcon_core.shell:create_environment_hook('tare_planner', 'cmake_prefix_path')
[0.737s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/hook/cmake_prefix_path.ps1'
[0.738s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/hook/cmake_prefix_path.dsv'
[0.738s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/hook/cmake_prefix_path.sh'
[0.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/lib'
[0.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/bin'
[0.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/lib/pkgconfig/tare_planner.pc'
[0.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/lib/python3.10/site-packages'
[0.739s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/bin'
[0.739s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.ps1'
[0.739s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.dsv'
[0.739s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.sh'
[0.740s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.bash'
[0.740s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.zsh'
[0.740s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/tare_planner/share/colcon-core/packages/tare_planner)
[0.741s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(tare_planner)
[0.741s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner' for CMake module files
[0.741s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner' for CMake config files
[0.741s] Level 1:colcon.colcon_core.shell:create_environment_hook('tare_planner', 'cmake_prefix_path')
[0.741s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/hook/cmake_prefix_path.ps1'
[0.742s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/hook/cmake_prefix_path.dsv'
[0.742s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/hook/cmake_prefix_path.sh'
[0.742s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/lib'
[0.742s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/bin'
[0.742s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/lib/pkgconfig/tare_planner.pc'
[0.742s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/lib/python3.10/site-packages'
[0.742s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/zhaoluye/install/tare_planner/bin'
[0.743s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.ps1'
[0.743s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.dsv'
[0.743s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.sh'
[0.743s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.bash'
[0.744s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.zsh'
[0.744s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/zhaoluye/install/tare_planner/share/colcon-core/packages/tare_planner)
[0.744s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.744s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.744s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.744s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.748s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.748s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.748s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.757s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.757s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/zhaoluye/install/local_setup.ps1'
[0.758s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/zhaoluye/install/_local_setup_util_ps1.py'
[0.759s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/zhaoluye/install/setup.ps1'
[0.760s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/zhaoluye/install/local_setup.sh'
[0.760s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/zhaoluye/install/_local_setup_util_sh.py'
[0.761s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/zhaoluye/install/setup.sh'
[0.762s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/zhaoluye/install/local_setup.bash'
[0.762s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/zhaoluye/install/setup.bash'
[0.763s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/zhaoluye/install/local_setup.zsh'
[0.763s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/zhaoluye/install/setup.zsh'
