[0.036s] Invoking command in '/home/<USER>/zhaoluye/build/tare_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/tare_planner -- -j16 -l16
[0.083s] [35m[1mConsolidate compiler generated dependencies of target lidar_model[0m
[0.083s] [35m[1mConsolidate compiler generated dependencies of target pointcloud_utils[0m
[0.084s] [35m[1mConsolidate compiler generated dependencies of target tare_visualizer[0m
[0.084s] [35m[1mConsolidate compiler generated dependencies of target exploration_path[0m
[0.084s] [35m[1mConsolidate compiler generated dependencies of target pointcloud_manager[0m
[0.085s] [35m[1mConsolidate compiler generated dependencies of target graph[0m
[0.086s] [35m[1mConsolidate compiler generated dependencies of target navigationBoundary[0m
[0.086s] [35m[1mConsolidate compiler generated dependencies of target misc_utils[0m
[0.091s] [35m[1mConsolidate compiler generated dependencies of target tsp_solver[0m
[0.092s] [35m[1mConsolidate compiler generated dependencies of target rolling_occupancy_grid[0m
[0.114s] [  5%] Built target pointcloud_manager
[0.114s] [ 10%] Built target exploration_path
[0.114s] [ 15%] Built target pointcloud_utils
[0.114s] [ 21%] Built target tsp_solver
[0.115s] [ 26%] Built target graph
[0.121s] [ 31%] Built target lidar_model
[0.122s] [ 36%] Built target tare_visualizer
[0.122s] [ 42%] Built target misc_utils
[0.123s] [ 47%] Built target navigationBoundary
[0.126s] [35m[1mConsolidate compiler generated dependencies of target viewpoint[0m
[0.127s] [ 52%] Built target rolling_occupancy_grid
[0.128s] [35m[1mConsolidate compiler generated dependencies of target rolling_grid[0m
[0.136s] [35m[1mConsolidate compiler generated dependencies of target planning_env[0m
[0.141s] [ 57%] Built target viewpoint
[0.153s] [ 63%] Built target rolling_grid
[0.153s] [ 68%] Built target planning_env
[0.159s] [35m[1mConsolidate compiler generated dependencies of target keypose_graph[0m
[0.193s] [ 73%] Built target keypose_graph
[0.197s] [35m[1mConsolidate compiler generated dependencies of target grid_world[0m
[0.221s] [ 78%] Built target grid_world
[0.231s] [35m[1mConsolidate compiler generated dependencies of target viewpoint_manager[0m
[0.257s] [ 84%] Built target viewpoint_manager
[0.269s] [35m[1mConsolidate compiler generated dependencies of target local_coverage_planner[0m
[0.289s] [ 89%] Built target local_coverage_planner
[0.300s] [35m[1mConsolidate compiler generated dependencies of target sensor_coverage_planner_ground[0m
[0.323s] [ 94%] Built target sensor_coverage_planner_ground
[0.334s] [35m[1mConsolidate compiler generated dependencies of target tare_planner_node[0m
[0.355s] [100%] Built target tare_planner_node
[0.364s] Invoked command in '/home/<USER>/zhaoluye/build/tare_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/tare_planner -- -j16 -l16
[0.365s] Invoking command in '/home/<USER>/zhaoluye/build/tare_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/tare_planner
[0.373s] -- Install configuration: ""
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/lib/tare_planner/navigationBoundary
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/lib/tare_planner/tare_planner_node
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore.launch
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_tunnel.launch
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_garage.launch
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_matterport.launch
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_indoor.launch
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_forest.launch
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_campus.launch
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//tare_planner_ground.rviz
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/campus.yaml
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/tunnel.yaml
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/garage.yaml
[0.373s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/indoor.yaml
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/matterport.yaml
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/forest.yaml
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/boundary.ply
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/package_run_dependencies/tare_planner
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/parent_prefix_path/tare_planner
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/ament_prefix_path.sh
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/ament_prefix_path.dsv
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/path.sh
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/path.dsv
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.bash
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.sh
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.zsh
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.dsv
[0.374s] -- Installing: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.dsv
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/packages/tare_planner
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/cmake/tare_plannerConfig.cmake
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/cmake/tare_plannerConfig-version.cmake
[0.374s] -- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.xml
[0.374s] Invoked command in '/home/<USER>/zhaoluye/build/tare_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/tare_planner
