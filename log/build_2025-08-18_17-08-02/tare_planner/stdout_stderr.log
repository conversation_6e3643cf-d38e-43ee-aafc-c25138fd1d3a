[35m[1mConsolidate compiler generated dependencies of target lidar_model[0m
[35m[1mConsolidate compiler generated dependencies of target pointcloud_utils[0m
[35m[1mConsolidate compiler generated dependencies of target tare_visualizer[0m
[35m[1mConsolidate compiler generated dependencies of target exploration_path[0m
[35m[1mConsolidate compiler generated dependencies of target pointcloud_manager[0m
[35m[1mConsolidate compiler generated dependencies of target graph[0m
[35m[1mConsolidate compiler generated dependencies of target navigationBoundary[0m
[35m[1mConsolidate compiler generated dependencies of target misc_utils[0m
[35m[1mConsolidate compiler generated dependencies of target tsp_solver[0m
[35m[1mConsolidate compiler generated dependencies of target rolling_occupancy_grid[0m
[  5%] Built target pointcloud_manager
[ 10%] Built target exploration_path
[ 15%] Built target pointcloud_utils
[ 21%] Built target tsp_solver
[ 26%] Built target graph
[ 31%] Built target lidar_model
[ 36%] Built target tare_visualizer
[ 42%] Built target misc_utils
[ 47%] Built target navigationBoundary
[35m[1mConsolidate compiler generated dependencies of target viewpoint[0m
[ 52%] Built target rolling_occupancy_grid
[35m[1mConsolidate compiler generated dependencies of target rolling_grid[0m
[35m[1mConsolidate compiler generated dependencies of target planning_env[0m
[ 57%] Built target viewpoint
[ 63%] Built target rolling_grid
[ 68%] Built target planning_env
[35m[1mConsolidate compiler generated dependencies of target keypose_graph[0m
[ 73%] Built target keypose_graph
[35m[1mConsolidate compiler generated dependencies of target grid_world[0m
[ 78%] Built target grid_world
[35m[1mConsolidate compiler generated dependencies of target viewpoint_manager[0m
[ 84%] Built target viewpoint_manager
[35m[1mConsolidate compiler generated dependencies of target local_coverage_planner[0m
[ 89%] Built target local_coverage_planner
[35m[1mConsolidate compiler generated dependencies of target sensor_coverage_planner_ground[0m
[ 94%] Built target sensor_coverage_planner_ground
[35m[1mConsolidate compiler generated dependencies of target tare_planner_node[0m
[100%] Built target tare_planner_node
-- Install configuration: ""
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/lib/tare_planner/navigationBoundary
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/lib/tare_planner/tare_planner_node
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore.launch
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_tunnel.launch
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_garage.launch
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_matterport.launch
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_indoor.launch
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_forest.launch
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_campus.launch
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//tare_planner_ground.rviz
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/campus.yaml
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/tunnel.yaml
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/garage.yaml
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/indoor.yaml
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/matterport.yaml
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/forest.yaml
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/boundary.ply
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/package_run_dependencies/tare_planner
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/parent_prefix_path/tare_planner
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/ament_prefix_path.sh
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/path.sh
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/path.dsv
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.bash
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.sh
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.zsh
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.dsv
-- Installing: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.dsv
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/packages/tare_planner
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/cmake/tare_plannerConfig.cmake
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/cmake/tare_plannerConfig-version.cmake
-- Up-to-date: /home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.xml
