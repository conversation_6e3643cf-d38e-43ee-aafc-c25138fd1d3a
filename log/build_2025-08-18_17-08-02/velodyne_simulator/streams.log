[0.060s] Invoking command in '/home/<USER>/zhaoluye/build/velodyne_simulator': CMAKE_PREFIX_PATH=/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_simulator -- -j16 -l16
[0.089s] Invoked command in '/home/<USER>/zhaoluye/build/velodyne_simulator' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/velodyne_simulator -- -j16 -l16
[0.090s] Invoking command in '/home/<USER>/zhaoluye/build/velodyne_simulator': CMAKE_PREFIX_PATH=/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_simulator
[0.097s] -- Install configuration: ""
[0.098s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/ament_index/resource_index/package_run_dependencies/velodyne_simulator
[0.098s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/ament_index/resource_index/parent_prefix_path/velodyne_simulator
[0.098s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/environment/ament_prefix_path.sh
[0.098s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/environment/ament_prefix_path.dsv
[0.098s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/environment/path.sh
[0.098s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/environment/path.dsv
[0.098s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/local_setup.bash
[0.098s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/local_setup.sh
[0.098s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/local_setup.zsh
[0.099s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/local_setup.dsv
[0.099s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.dsv
[0.099s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/ament_index/resource_index/packages/velodyne_simulator
[0.099s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/cmake/velodyne_simulatorConfig.cmake
[0.100s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/cmake/velodyne_simulatorConfig-version.cmake
[0.100s] -- Up-to-date: /home/<USER>/zhaoluye/install/velodyne_simulator/share/velodyne_simulator/package.xml
[0.100s] Invoked command in '/home/<USER>/zhaoluye/build/velodyne_simulator' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/zhaoluye/install/velodyne_gazebo_plugins:/home/<USER>/zhaoluye/install/velodyne_description:/home/<USER>/zhaoluye/install/waypoint_rviz_plugin:/home/<USER>/zhaoluye/install/waypoint_example:/home/<USER>/zhaoluye/install/visualization_tools:/home/<USER>/zhaoluye/install/velodyne_simulator:/home/<USER>/zhaoluye/install/vehicle_simulator:/home/<USER>/zhaoluye/install/terrain_analysis_ext:/home/<USER>/zhaoluye/install/terrain_analysis:/home/<USER>/zhaoluye/install/tare_planner:/home/<USER>/zhaoluye/install/sensor_scan_generation:/home/<USER>/zhaoluye/install/local_planner:/home/<USER>/zhaoluye/install/loam_interface:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/velodyne_simulator
