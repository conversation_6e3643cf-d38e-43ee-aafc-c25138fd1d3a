[35m[1mConsolidate compiler generated dependencies of target visualizationTools[0m
[  0%] Built target ament_cmake_python_copy_visualization_tools
[100%] Built target visualizationTools
running egg_info
writing visualization_tools.egg-info/PKG-INFO
writing dependency_links to visualization_tools.egg-info/dependency_links.txt
writing top-level names to visualization_tools.egg-info/top_level.txt
reading manifest file 'visualization_tools.egg-info/SOURCES.txt'
writing manifest file 'visualization_tools.egg-info/SOURCES.txt'
[100%] Built target ament_cmake_python_build_visualization_tools_egg
-- Install configuration: ""
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/lib/visualization_tools/visualizationTools
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/launch
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/launch/visualization_tools.launch
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/pythonpath.sh
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/pythonpath.dsv
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info
-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/dependency_links.txt
-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/SOURCES.txt
-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/top_level.txt
-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/PKG-INFO
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools/__init__.py
Listing '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools'...
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/lib/visualization_tools/realTimePlot.py
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/ament_index/resource_index/package_run_dependencies/visualization_tools
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/ament_index/resource_index/parent_prefix_path/visualization_tools
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.sh
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/path.sh
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/path.dsv
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.bash
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.sh
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.zsh
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.dsv
-- Installing: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.dsv
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/ament_index/resource_index/packages/visualization_tools
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig.cmake
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig-version.cmake
-- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.xml
