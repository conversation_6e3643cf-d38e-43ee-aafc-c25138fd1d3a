[0.039s] Invoking command in '/home/<USER>/zhaoluye/build/visualization_tools': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/visualization_tools -- -j16 -l16
[0.094s] [35m[1mConsolidate compiler generated dependencies of target visualizationTools[0m
[0.114s] [  0%] Built target ament_cmake_python_copy_visualization_tools
[0.119s] [100%] Built target visualizationTools
[0.272s] running egg_info
[0.274s] writing visualization_tools.egg-info/PKG-INFO
[0.274s] writing dependency_links to visualization_tools.egg-info/dependency_links.txt
[0.274s] writing top-level names to visualization_tools.egg-info/top_level.txt
[0.275s] reading manifest file 'visualization_tools.egg-info/SOURCES.txt'
[0.276s] writing manifest file 'visualization_tools.egg-info/SOURCES.txt'
[0.294s] [100%] Built target ament_cmake_python_build_visualization_tools_egg
[0.302s] Invoked command in '/home/<USER>/zhaoluye/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/visualization_tools -- -j16 -l16
[0.303s] Invoking command in '/home/<USER>/zhaoluye/build/visualization_tools': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/visualization_tools
[0.312s] -- Install configuration: ""
[0.312s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/lib/visualization_tools/visualizationTools
[0.312s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/launch
[0.312s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/launch/visualization_tools.launch
[0.313s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/pythonpath.sh
[0.313s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/pythonpath.dsv
[0.313s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info
[0.313s] -- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/dependency_links.txt
[0.313s] -- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/SOURCES.txt
[0.313s] -- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/top_level.txt
[0.313s] -- Installing: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/PKG-INFO
[0.313s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools
[0.313s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools/__init__.py
[0.336s] Listing '/home/<USER>/zhaoluye/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools'...
[0.339s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/lib/visualization_tools/realTimePlot.py
[0.339s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/ament_index/resource_index/package_run_dependencies/visualization_tools
[0.339s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/ament_index/resource_index/parent_prefix_path/visualization_tools
[0.339s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.sh
[0.339s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.dsv
[0.339s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/path.sh
[0.339s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/environment/path.dsv
[0.339s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.bash
[0.340s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.sh
[0.340s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.zsh
[0.340s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/local_setup.dsv
[0.340s] -- Installing: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.dsv
[0.340s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/ament_index/resource_index/packages/visualization_tools
[0.340s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig.cmake
[0.340s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig-version.cmake
[0.341s] -- Up-to-date: /home/<USER>/zhaoluye/install/visualization_tools/share/visualization_tools/package.xml
[0.341s] Invoked command in '/home/<USER>/zhaoluye/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/visualization_tools
