[0.036s] Invoking command in '/home/<USER>/zhaoluye/build/sensor_scan_generation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/sensor_scan_generation -- -j16 -l16
[0.068s] [35m[1mConsolidate compiler generated dependencies of target sensorScanGeneration[0m
[0.101s] [100%] Built target sensorScanGeneration
[0.112s] Invoked command in '/home/<USER>/zhaoluye/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/sensor_scan_generation -- -j16 -l16
[0.116s] Invoking command in '/home/<USER>/zhaoluye/build/sensor_scan_generation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/sensor_scan_generation
[0.125s] -- Install configuration: ""
[0.125s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/lib/sensor_scan_generation/sensorScanGeneration
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/launch
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/launch/sensor_scan_generation.launch
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/ament_index/resource_index/package_run_dependencies/sensor_scan_generation
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/ament_index/resource_index/parent_prefix_path/sensor_scan_generation
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/environment/ament_prefix_path.sh
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/environment/ament_prefix_path.dsv
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/environment/path.sh
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/environment/path.dsv
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.bash
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.sh
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.zsh
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.dsv
[0.126s] -- Installing: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv
[0.126s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/ament_index/resource_index/packages/sensor_scan_generation
[0.127s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/cmake/sensor_scan_generationConfig.cmake
[0.127s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/cmake/sensor_scan_generationConfig-version.cmake
[0.127s] -- Up-to-date: /home/<USER>/zhaoluye/install/sensor_scan_generation/share/sensor_scan_generation/package.xml
[0.156s] Invoked command in '/home/<USER>/zhaoluye/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/sensor_scan_generation
