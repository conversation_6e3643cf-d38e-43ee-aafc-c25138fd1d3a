[0.036s] Invoking command in '/home/<USER>/zhaoluye/build/local_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/local_planner -- -j16 -l16
[0.071s] [35m[1mConsolidate compiler generated dependencies of target pathFollower[0m
[0.072s] [35m[1mConsolidate compiler generated dependencies of target localPlanner[0m
[0.104s] [ 50%] Built target pathFollower
[0.109s] [100%] Built target localPlanner
[0.156s] Invoked command in '/home/<USER>/zhaoluye/build/local_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/zhaoluye/build/local_planner -- -j16 -l16
[0.157s] Invoking command in '/home/<USER>/zhaoluye/build/local_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/local_planner
[0.163s] -- Install configuration: ""
[0.163s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/lib/local_planner/localPlanner
[0.163s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/lib/local_planner/pathFollower
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/launch
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/launch/local_planner.launch.py
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/launch/local_planner.launch
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/startPaths.ply
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/correspondences.txt
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/pathList.ply
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/paths.ply
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/paths/path_generator.m
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/config
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/ament_index/resource_index/package_run_dependencies/local_planner
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/ament_index/resource_index/parent_prefix_path/local_planner
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/environment/ament_prefix_path.sh
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/environment/ament_prefix_path.dsv
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/environment/path.sh
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/environment/path.dsv
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/local_setup.bash
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/local_setup.sh
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/local_setup.zsh
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/local_setup.dsv
[0.164s] -- Installing: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.dsv
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/ament_index/resource_index/packages/local_planner
[0.164s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/cmake/local_plannerConfig.cmake
[0.165s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/cmake/local_plannerConfig-version.cmake
[0.165s] -- Up-to-date: /home/<USER>/zhaoluye/install/local_planner/share/local_planner/package.xml
[0.207s] Invoked command in '/home/<USER>/zhaoluye/build/local_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/zhaoluye/build/local_planner
