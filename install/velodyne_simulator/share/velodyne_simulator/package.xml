<?xml version="1.0"?>
<package format="3">
  <name>velodyne_simulator</name>
  <version>2.0.2</version>
  <description>
    Metapackage allowing easy installation of Velodyne simulation components.
  </description>

  <license>BSD</license>
  <author><PERSON></author>
  <maintainer email="k<PERSON><PERSON><PERSON>@dataspeedinc.com"><PERSON></maintainer>
  <url type="website">http://wiki.ros.org/velodyne_simulator</url>
  <url type="repository">https://bitbucket.org/dataspeedinc/velodyne_simulator</url>
  <url type="bugtracker">https://bitbucket.org/dataspeedinc/velodyne_simulator/issues</url>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <exec_depend>velodyne_description</exec_depend>
  <exec_depend>velodyne_gazebo_plugins</exec_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>

</package>
