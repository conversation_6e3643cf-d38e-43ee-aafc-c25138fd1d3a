#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    # 获取包路径
    pkg_share = FindPackageShare('local_planner')
    
    # 参数文件路径 - 使用统一的integrated_nav_params.yaml
    integrated_params_file = PathJoinSubstitution([
        FindPackageShare('integrated_navigation'), 'config', 'integrated_nav_params.yaml'
    ])
    
    # Launch参数声明
    declare_sensor_offset_x = DeclareLaunchArgument(
        'sensorOffsetX',
        default_value='0.0',
        description='Sensor offset in X direction (m)'
    )
    
    declare_sensor_offset_y = DeclareLaunchArgument(
        'sensorOffsetY', 
        default_value='0.0',
        description='Sensor offset in Y direction (m)'
    )
    
    declare_camera_offset_z = DeclareLaunchArgument(
        'cameraOffsetZ',
        default_value='0.0', 
        description='Camera offset in Z direction (m)'
    )
    
    declare_goal_x = DeclareLaunchArgument(
        'goalX',
        default_value='0.0',
        description='Goal position X coordinate (m)'
    )
    
    declare_goal_y = DeclareLaunchArgument(
        'goalY',
        default_value='0.0',
        description='Goal position Y coordinate (m)'
    )
    
    # 获取launch配置
    sensor_offset_x = LaunchConfiguration('sensorOffsetX')
    sensor_offset_y = LaunchConfiguration('sensorOffsetY')
    camera_offset_z = LaunchConfiguration('cameraOffsetZ')
    goal_x = LaunchConfiguration('goalX')
    goal_y = LaunchConfiguration('goalY')
    
    # localPlanner节点
    local_planner_node = Node(
        package='local_planner',
        executable='localPlanner',
        name='localPlanner',
        output='screen',
        parameters=[
            {
                'pathFolder': PathJoinSubstitution([pkg_share, 'paths']),
                'vehicleLength': 0.6,
                'vehicleWidth': 0.6,
                'sensorOffsetX': sensor_offset_x,
                'sensorOffsetY': sensor_offset_y,
                'twoWayDrive': True,
                'laserVoxelSize': 0.05,
                'terrainVoxelSize': 0.2,
                'useTerrainAnalysis': True,
                'checkObstacle': True,
                'checkRotObstacle': False,
                'adjacentRange': 4.25,
                'obstacleHeightThre': 0.15,
                'groundHeightThre': 0.1,
                'costHeightThre': 0.1,
                'costScore': 0.02,
                'useCost': False,
                'pointPerPathThre': 2,
                'minRelZ': -0.5,
                'maxRelZ': 0.25,
                'maxSpeed': 2.0,
                'dirWeight': 0.02,
                'dirThre': 90.0,
                'dirToVehicle': False,
                'pathScale': 1.25,
                'minPathScale': 0.75,
                'pathScaleStep': 0.25,
                'pathScaleBySpeed': True,
                'minPathRange': 1.0,
                'pathRangeStep': 0.5,
                'pathRangeBySpeed': True,
                'pathCropByGoal': True,
                'autonomyMode': True,
                'autonomySpeed': 2.0,
                'joyToSpeedDelay': 2.0,
                'joyToCheckObstacleDelay': 5.0,
                'goalClearRange': 0.5,
                'goalX': goal_x,
                'goalY': goal_y
            }
        ]
    )
    
    # pathFollower节点 - 使用统一的参数文件
    path_follower_node = Node(
        package='local_planner',
        executable='pathFollower',
        name='pathFollower',
        output='screen',
        parameters=[
            integrated_params_file,  # 从统一的参数文件加载
            {
                # 覆盖部分参数（如果需要从launch参数设置）
                'sensorOffsetX': sensor_offset_x,
                'sensorOffsetY': sensor_offset_y,
            }
        ]
    )
    
    # TF2静态变换发布器 - 车辆坐标系
    vehicle_tf_publisher = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='vehicleTransPublisher',
        arguments=[
            '--x', ['-', sensor_offset_x],
            '--y', ['-', sensor_offset_y], 
            '--z', '0',
            '--roll', '0',
            '--pitch', '0', 
            '--yaw', '0',
            '--frame-id', 'sensor',
            '--child-frame-id', 'vehicle'
        ]
    )
    
    # TF2静态变换发布器 - 传感器坐标系  
    sensor_tf_publisher = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='sensorTransPublisher',
        arguments=[
            '--x', '0',
            '--y', '0',
            '--z', camera_offset_z,
            '--roll', '-1.5707963',
            '--pitch', '0',
            '--yaw', '-1.5707963', 
            '--frame-id', 'sensor',
            '--child-frame-id', 'camera'
        ]
    )
    
    # 构建launch描述
    ld = LaunchDescription()
    
    # 添加参数声明
    ld.add_action(declare_sensor_offset_x)
    ld.add_action(declare_sensor_offset_y)
    ld.add_action(declare_camera_offset_z)
    ld.add_action(declare_goal_x)
    ld.add_action(declare_goal_y)
    
    # 添加节点
    ld.add_action(local_planner_node)
    ld.add_action(path_follower_node)
    ld.add_action(vehicle_tf_publisher)
    ld.add_action(sensor_tf_publisher)
    
    return ld