Panels:
  - Class: rviz_common/Displays
    Help Height: 0
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /Global1
        - /Local1
      Splitter Ratio: 0.42941176891326904
    Tree Height: 441
  - Class: rviz_common/Selection
    Name: Selection
  - Class: rviz_common/Tool Properties
    Expanded: ~
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz_common/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz_common/Time
    Experimental: false
    Name: Time
    SyncMode: 0
    SyncSource: ExploredAreas
Visualization Manager:
  Class: ""
  Displays:
    - Class: rviz_common/Group
      Displays:
        - Alpha: 0.8999999761581421
          Autocompute Intensity Bounds: false
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 255; 255; 255
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 20
          Min Color: 0; 0; 0
          Min Intensity: -1
          Name: KeyposeGraphNodes
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 3
          Size (m): 0.30000001192092896
          Style: Boxes
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /keypose_graph_cloud
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Class: rviz_default_plugins/Marker
          Enabled: false
          Name: KeyposeGraphEdge
          Namespaces:
            {}
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /keypose_graph_edge_marker
          Value: false
        - Class: rviz_default_plugins/Marker
          Enabled: true
          Name: ExploringSubspaces
          Namespaces:
            "": true
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /tare_visualizer/exploring_subspaces
          Value: true
        - Alpha: 1
          Buffer Length: 1
          Class: rviz_default_plugins/Path
          Color: 98; 240; 231
          Enabled: true
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Billboards
          Line Width: 0.20000000298023224
          Name: GlobalPath
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /global_path
          Value: true
        - Alpha: 1
          Autocompute Intensity Bounds: false
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 255; 255; 255
          Color Transformer: FlatColor
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 2
          Min Color: 0; 0; 0
          Min Intensity: 0
          Name: OverallObjectSurfaces
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 3
          Size (m): 0.10000000149011612
          Style: Flat Squares
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /planner_cloud
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Alpha: 0.10000000149011612
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 255; 255; 255
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 4096
          Min Color: 0; 0; 0
          Min Intensity: 0
          Name: OverallMap
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 2
          Size (m): 0.009999999776482582
          Style: Points
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /overall_map
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
        - Alpha: 0.10000000149011612
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 0; 170; 255
          Color Transformer: FlatColor
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 0
          Min Color: 0; 0; 0
          Min Intensity: 0
          Name: ExploredAreas
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 2
          Size (m): 0.009999999776482582
          Style: Points
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /explored_areas
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
        - Alpha: 1
          Class: rviz_default_plugins/Polygon
          Color: 25; 255; 0
          Enabled: false
          Name: Boundary
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /navigation_boundary
          Value: false
      Enabled: true
      Name: Global
    - Class: rviz_common/Group
      Displays:
        - Class: rviz_default_plugins/Marker
          Enabled: true
          Name: LocalPlanningHorizon
          Namespaces:
            "": true
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /tare_visualizer/local_planning_horizon
          Value: true
        - Alpha: 0.5
          Autocompute Intensity Bounds: false
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 237; 212; 0
          Color Transformer: FlatColor
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 100
          Min Color: 0; 0; 0
          Min Intensity: -1
          Name: ViewpointCandidates
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 3
          Size (m): 0.5
          Style: Spheres
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /viewpoint_vis_cloud
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Alpha: 1
          Autocompute Intensity Bounds: false
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 245; 121; 0
          Color Transformer: FlatColor
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 10
          Min Color: 0; 0; 0
          Min Intensity: 0
          Name: SelectedViewPoints
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 3
          Size (m): 1
          Style: Spheres
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /selected_viewpoint_vis_cloud
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
        - Alpha: 0.30000001192092896
          Autocompute Intensity Bounds: false
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 255; 255; 255
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 2
          Min Color: 0; 0; 0
          Min Intensity: 0
          Name: ViewpointToTrack
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 3
          Size (m): 2
          Style: Spheres
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /lookahead_point_cloud
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Alpha: 1
          Buffer Length: 1
          Class: rviz_default_plugins/Path
          Color: 45; 66; 253
          Enabled: true
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Billboards
          Line Width: 0.20000000298023224
          Name: LocalPath
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /local_path
          Value: true
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 204; 0; 0
          Color Transformer: FlatColor
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 1
          Min Color: 0; 0; 0
          Min Intensity: 0
          Name: ObjectSurfacesToCover
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 2
          Size (m): 0.20000000298023224
          Style: Points
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /uncovered_cloud
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 204; 0; 0
          Color Transformer: FlatColor
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 8
          Min Color: 0; 0; 0
          Min Intensity: 0
          Name: FrontierSurfacesToCover
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 2
          Size (m): 0.30000001192092896
          Style: Points
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /uncovered_frontier_cloud
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
        - Alpha: 1
          Class: rviz_default_plugins/PointStamped
          Color: 204; 41; 204
          Enabled: true
          History Length: 1
          Name: Waypoint
          Radius: 1
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /way_point
          Value: true
        - Class: rviz_default_plugins/Axes
          Enabled: true
          Length: 4
          Name: Vehicle
          Radius: 0.4000000059604645
          Reference Frame: sensor
          Value: true
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz_default_plugins/PointCloud2
          Color: 255; 255; 255
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Max Intensity: 1
          Min Color: 0; 0; 0
          Min Intensity: 1
          Name: FreePaths
          Position Transformer: XYZ
          Selectable: true
          Size (Pixels): 3
          Size (m): 0.009999999776482582
          Style: Flat Squares
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /free_paths
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Alpha: 1
          Buffer Length: 1
          Class: rviz_default_plugins/Path
          Color: 25; 255; 0
          Enabled: false
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Billboards
          Line Width: 0.10000000149011612
          Name: Path
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /path
          Value: false
      Enabled: true
      Name: Local
  Enabled: true
  Global Options:
    Background Color: 0; 0; 0
    Fixed Frame: map
    Frame Rate: 15
  Name: root
  Tools:
    - Class: rviz_default_plugins/MoveCamera
  Transformation:
    Current:
      Class: rviz_default_plugins/TF
  Value: true
  Views:
    Current:
      Class: rviz_default_plugins/Orbit
      Distance: 169.4073486328125
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Focal Point:
        X: -9.871967315673828
        Y: 46.698097229003906
        Z: 0.704297661781311
      Focal Shape Fixed Size: false
      Focal Shape Size: 0.05000000074505806
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Pitch: 1.5697963237762451
      Target Frame: map
      Value: XYOrbit (rviz)
      Yaw: 3.140395164489746
    Saved: ~
Window Geometry:
  Displays:
    collapsed: true
  Height: 818
  Hide Left Dock: true
  Hide Right Dock: false
  QMainWindow State: 000000ff00000000fd000000040000000000000156000002fffc0200000008fb0000001200530065006c0065006300740069006f006e0000000016000001320000005c00fffffffb0000001e0054006f006f006c002000500072006f007000650072007400690065007302000001ed000001df00000185000000a3fb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb000000100044006900730070006c0061007900730000000016000002ff000000c900fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261000000010000010f000003ccfc0200000003fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000000a005600690065007700730000000016000003cc000000a400fffffffb0000001200530065006c0065006300740069006f006e010000025a000000b200000000000000000000000200000490000000a9fc0100000001fb0000000a00560069006500770073030000004e00000080000002e1000001970000000300000a000000003efc0100000002fb0000000800540069006d0065000000000000000a00000002fb00fffffffb0000000800540069006d00650100000000000004500000000000000000000005a7000002ff00000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730000000000ffffffff0000000000000000
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: false
  Width: 1447
  X: 58
  Y: 27
