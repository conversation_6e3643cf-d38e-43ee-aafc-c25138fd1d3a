/home/<USER>/zhaoluye/install/tare_planner/lib/tare_planner/navigationBoundary
/home/<USER>/zhaoluye/install/tare_planner/lib/tare_planner/tare_planner_node
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore.launch
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_tunnel.launch
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_garage.launch
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_matterport.launch
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_indoor.launch
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_forest.launch
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//explore_campus.launch
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner//tare_planner_ground.rviz
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/campus.yaml
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/tunnel.yaml
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/garage.yaml
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/indoor.yaml
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/matterport.yaml
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/forest.yaml
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/boundary.ply
/home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/package_run_dependencies/tare_planner
/home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/parent_prefix_path/tare_planner
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/ament_prefix_path.sh
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/ament_prefix_path.dsv
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/path.sh
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/environment/path.dsv
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.bash
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.sh
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.zsh
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/local_setup.dsv
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.dsv
/home/<USER>/zhaoluye/install/tare_planner/share/ament_index/resource_index/packages/tare_planner
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/cmake/tare_plannerConfig.cmake
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/cmake/tare_plannerConfig-version.cmake
/home/<USER>/zhaoluye/install/tare_planner/share/tare_planner/package.xml