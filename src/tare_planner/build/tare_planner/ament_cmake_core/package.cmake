set(_AMENT_PACKAGE_NAME "tare_planner")
set(tare_planner_VERSION "0.0.1")
set(tare_planner_MAINTAINER "<PERSON> <PERSON> <<EMAIL>>")
set(tare_planner_BUILD_DEPENDS "rclcpp" "std_msgs" "nav_msgs" "sensor_msgs" "geometry_msgs" "visualization_msgs" "tf2" "tf2_ros" "pcl_ros" "pcl_conversions" "libgoogle-glog-dev")
set(tare_planner_BUILDTOOL_DEPENDS "ament_cmake")
set(tare_planner_BUILD_EXPORT_DEPENDS "rclcpp" "std_msgs" "nav_msgs" "sensor_msgs" "geometry_msgs" "visualization_msgs" "tf2" "tf2_ros" "pcl_ros" "pcl_conversions" "libgoogle-glog-dev")
set(tare_planner_BUILDTOOL_EXPORT_DEPENDS )
set(tare_planner_EXEC_DEPENDS "rclcpp" "std_msgs" "nav_msgs" "sensor_msgs" "geometry_msgs" "visualization_msgs" "tf2" "tf2_ros" "pcl_ros" "pcl_conversions" "libgoogle-glog-dev")
set(tare_planner_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(tare_planner_GROUP_DEPENDS )
set(tare_planner_MEMBER_OF_GROUPS )
set(tare_planner_DEPRECATED "")
set(tare_planner_EXPORT_TAGS)
list(APPEND tare_planner_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
