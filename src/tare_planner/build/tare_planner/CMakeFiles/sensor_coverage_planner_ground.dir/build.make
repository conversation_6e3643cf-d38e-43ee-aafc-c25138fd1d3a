# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/zhaoluye/src/tare_planner

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/zhaoluye/src/tare_planner/build/tare_planner

# Include any dependencies generated for this target.
include CMakeFiles/sensor_coverage_planner_ground.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/sensor_coverage_planner_ground.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/sensor_coverage_planner_ground.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/sensor_coverage_planner_ground.dir/flags.make

CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.o: CMakeFiles/sensor_coverage_planner_ground.dir/flags.make
CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.o: ../../src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp
CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.o: CMakeFiles/sensor_coverage_planner_ground.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/zhaoluye/src/tare_planner/build/tare_planner/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.o -MF CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.o.d -o CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.o -c /home/<USER>/zhaoluye/src/tare_planner/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp

CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/zhaoluye/src/tare_planner/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp > CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.i

CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/zhaoluye/src/tare_planner/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp -o CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.s

# Object files for target sensor_coverage_planner_ground
sensor_coverage_planner_ground_OBJECTS = \
"CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.o"

# External object files for target sensor_coverage_planner_ground
sensor_coverage_planner_ground_EXTERNAL_OBJECTS =

libsensor_coverage_planner_ground.a: CMakeFiles/sensor_coverage_planner_ground.dir/src/sensor_coverage_planner/sensor_coverage_planner_ground.cpp.o
libsensor_coverage_planner_ground.a: CMakeFiles/sensor_coverage_planner_ground.dir/build.make
libsensor_coverage_planner_ground.a: CMakeFiles/sensor_coverage_planner_ground.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/zhaoluye/src/tare_planner/build/tare_planner/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libsensor_coverage_planner_ground.a"
	$(CMAKE_COMMAND) -P CMakeFiles/sensor_coverage_planner_ground.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sensor_coverage_planner_ground.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/sensor_coverage_planner_ground.dir/build: libsensor_coverage_planner_ground.a
.PHONY : CMakeFiles/sensor_coverage_planner_ground.dir/build

CMakeFiles/sensor_coverage_planner_ground.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/sensor_coverage_planner_ground.dir/cmake_clean.cmake
.PHONY : CMakeFiles/sensor_coverage_planner_ground.dir/clean

CMakeFiles/sensor_coverage_planner_ground.dir/depend:
	cd /home/<USER>/zhaoluye/src/tare_planner/build/tare_planner && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/zhaoluye/src/tare_planner /home/<USER>/zhaoluye/src/tare_planner /home/<USER>/zhaoluye/src/tare_planner/build/tare_planner /home/<USER>/zhaoluye/src/tare_planner/build/tare_planner /home/<USER>/zhaoluye/src/tare_planner/build/tare_planner/CMakeFiles/sensor_coverage_planner_ground.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/sensor_coverage_planner_ground.dir/depend

