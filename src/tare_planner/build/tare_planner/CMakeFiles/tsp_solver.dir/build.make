# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/zhaoluye/src/tare_planner

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/zhaoluye/src/tare_planner/build/tare_planner

# Include any dependencies generated for this target.
include CMakeFiles/tsp_solver.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/tsp_solver.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/tsp_solver.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/tsp_solver.dir/flags.make

CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.o: CMakeFiles/tsp_solver.dir/flags.make
CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.o: ../../src/tsp_solver/tsp_solver.cpp
CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.o: CMakeFiles/tsp_solver.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/zhaoluye/src/tare_planner/build/tare_planner/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.o -MF CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.o.d -o CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.o -c /home/<USER>/zhaoluye/src/tare_planner/src/tsp_solver/tsp_solver.cpp

CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/zhaoluye/src/tare_planner/src/tsp_solver/tsp_solver.cpp > CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.i

CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/zhaoluye/src/tare_planner/src/tsp_solver/tsp_solver.cpp -o CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.s

# Object files for target tsp_solver
tsp_solver_OBJECTS = \
"CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.o"

# External object files for target tsp_solver
tsp_solver_EXTERNAL_OBJECTS =

libtsp_solver.a: CMakeFiles/tsp_solver.dir/src/tsp_solver/tsp_solver.cpp.o
libtsp_solver.a: CMakeFiles/tsp_solver.dir/build.make
libtsp_solver.a: CMakeFiles/tsp_solver.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/zhaoluye/src/tare_planner/build/tare_planner/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libtsp_solver.a"
	$(CMAKE_COMMAND) -P CMakeFiles/tsp_solver.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/tsp_solver.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/tsp_solver.dir/build: libtsp_solver.a
.PHONY : CMakeFiles/tsp_solver.dir/build

CMakeFiles/tsp_solver.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/tsp_solver.dir/cmake_clean.cmake
.PHONY : CMakeFiles/tsp_solver.dir/clean

CMakeFiles/tsp_solver.dir/depend:
	cd /home/<USER>/zhaoluye/src/tare_planner/build/tare_planner && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/zhaoluye/src/tare_planner /home/<USER>/zhaoluye/src/tare_planner /home/<USER>/zhaoluye/src/tare_planner/build/tare_planner /home/<USER>/zhaoluye/src/tare_planner/build/tare_planner /home/<USER>/zhaoluye/src/tare_planner/build/tare_planner/CMakeFiles/tsp_solver.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/tsp_solver.dir/depend

