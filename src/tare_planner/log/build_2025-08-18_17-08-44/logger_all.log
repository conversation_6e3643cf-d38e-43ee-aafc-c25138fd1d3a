[0.065s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.065s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x77d89a858e50>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x77d89a858a00>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x77d89a858a00>>)
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.162s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/zhaoluye/src/tare_planner'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.171s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_cmake' and name 'tare_planner'
[0.171s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.171s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.172s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.172s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.172s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.189s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.189s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.190s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 13 installed packages in /home/<USER>/zhaoluye/install
[0.191s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 473 installed packages in /opt/ros/humble
[0.193s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.224s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_args' from command line to 'None'
[0.224s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_target' from command line to 'None'
[0.224s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.224s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'tare_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.225s] DEBUG:colcon.colcon_core.verb:Building package 'tare_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/zhaoluye/src/tare_planner/build/tare_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/zhaoluye/src/tare_planner/install/tare_planner', 'merge_install': False, 'path': '/home/<USER>/zhaoluye/src/tare_planner', 'symlink_install': False, 'test_result_base': None}
[0.225s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.225s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.226s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/zhaoluye/src/tare_planner' with build type 'ament_cmake'
[0.226s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/zhaoluye/src/tare_planner'
[0.227s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.227s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.227s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.233s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/zhaoluye/src/tare_planner/build/tare_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/zhaoluye/src/tare_planner -DCMAKE_INSTALL_PREFIX=/home/<USER>/zhaoluye/src/tare_planner/install/tare_planner
